// Listen for messages from the background script
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  console.log("Content script received message:", message, "\n--------------sender:", sender);

    let result = { success: false, message: "Unknown action or error." }

    try {
      switch (message.action) {
        case "findElementBySelector":
          result = findElementBySelector(message.selector)
          break
        case "clickElement":
          result = clickElement(message.selector)
          break
        case "typeText":
          result = typeText(message.selector, message.text)
          break
        case "getElementPosition":
          result = getElementPosition(message.selector)
          break
        // Add more cases for other element interaction actions
        default:
          result = { success: false, message: `Unknown content script action: ${message.action}` }
      }
    } catch (error: any) {
      result = { success: false, message: error.message, error: error.toString() } as any
    }

    // Send the result back to the background script
    sendResponse(result)
    // Optionally, also send a message using chrome.runtime.sendMessage for more complex data flow
    // chrome.runtime.sendMessage({ type: "content_script_result", data: result })

  // Return true to indicate that you will send a response asynchronously
  return true
})


// API Functions for element interaction

function findElementBySelector(selector: string) {
  const element = document.querySelector(selector)
  if (element) {
    return { success: true, message: "Element found.", selector: selector }
  } else {
    return { success: false, message: "Element not found.", selector: selector }
  }
}

function clickElement(selector: string) {
  const element = document.querySelector(selector)
  if (element) {
    (element as HTMLElement).click()
    return { success: true, message: "Element clicked.", selector: selector }
  } else {
    return { success: false, message: "Element not found for click.", selector: selector }
  }
}

function typeText(selector: string, text: string) {
  const element = document.querySelector(selector)
  if (element) {
    if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
      (element as HTMLInputElement).value = text
      // Dispatch input and change events to simulate user typing
      element.dispatchEvent(new Event('input', { bubbles: true }))
      element.dispatchEvent(new Event('change', { bubbles: true }))
      return { success: true, message: "Text typed.", selector: selector, text: text }
    } else {
      return { success: false, message: "Element is not an input or textarea.", selector: selector }
    }
  } else {
    return { success: false, message: "Element not found for typing.", selector: selector }
  }
}

function getElementPosition(selector: string) {
  const element = document.querySelector(selector)
  if (element) {
    const rect = element.getBoundingClientRect()
    const documentScrollTop = window.scrollY || document.documentElement.scrollTop
    const documentScrollLeft = window.scrollX || document.documentElement.scrollLeft

    return {
      success: true,
      message: "Element position found.",
      selector: selector,
      screen: {
        top: rect.top,
        left: rect.left,
        width: rect.width,
        height: rect.height
      },
      document: {
        top: rect.top + documentScrollTop,
        left: rect.left + documentScrollLeft,
        width: rect.width,
        height: rect.height
      }
    }
  } else {
    return { success: false, message: "Element not found for position.", selector: selector }
  }
}