import pfs from 'fs/promises'

export const tsvToJson = async (filePath: string) => {
    const tsv = await pfs.readFile(filePath, 'utf-8')
    const lines = tsv.split('\n').filter(x => !!x)
    const headers = lines[0].split('\t')
    const data = lines.slice(1).map(line => {
        const values = line.split('\t')
        return headers.reduce((obj, header, i) => {
            obj[header] = values[i]
            return obj
        }, {} as any)
    })
    return data
}