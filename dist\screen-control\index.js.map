{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/screen-control/index.ts"], "names": [], "mappings": ";;;;;AAKA,gCAkBC;AAtBD,4EAA2C;AAC3C,4EAA0C;AAC1C,2DAA0D;AAEnD,KAAK,UAAU,UAAU;IAC5B,IAAI,CAAC;QACD,MAAM,KAAK,GAAG,IAAI,IAAI,EAAE,CAAA;QACxB,MAAM,MAAM,GAAG,MAAM,IAAA,4BAAU,EAAC,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,CAAA;QAClD,MAAM,OAAO,GAAG,IAAI,IAAI,EAAE,CAAA;QAC1B,MAAM,IAAI,GAAG,MAAM,4BAAS,CAAC,SAAS,CAAC,MAAM,EAAE;YAC3C,IAAI,EAAE,KAAK;YACX,OAAO,EAAE,CAAC,MAAM,CAAC;YACjB,MAAM,EAAE,mDAAmD;SAC9D,CAAC,CAAA;QACF,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAA;QACtB,OAAO,CAAC,GAAG,CAAC,UAAU,GAAG,CAAC,OAAO,EAAE,GAAG,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,CAAA;QAC1D,OAAO,CAAC,GAAG,CAAC,eAAe,OAAO,CAAC,OAAO,EAAE,GAAG,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,CAAA;QACnE,OAAO,CAAC,GAAG,CAAC,QAAQ,GAAG,CAAC,OAAO,EAAE,GAAG,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,CAAA;QAC1D,OAAO,iBAAiB,CAAC,IAAA,+BAAU,EAAC,IAAI,CAAC,EAAE,GAAG,CAAC,CAAA;IACnD,CAAC;IAAC,OAAO,GAAG,EAAE,CAAC;QACX,OAAO,CAAC,KAAK,CAAC,SAAS,EAAE,GAAG,CAAC,CAAA;IACjC,CAAC;AACL,CAAC;AAED,SAAS,iBAAiB,CAAC,IAAS,EAAE,aAAa,GAAG,IAAI;IACtD,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,CAAE;SAC5D,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,CAAE;SACzC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC,CAAC,UAAU,CAAE;SAC7C,UAAU,CAAC,QAAQ,CAAA;IACxB,MAAM,OAAO,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,MAAM,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC,CAAU,CAAA;IAC1F,MAAM,IAAI,GAAS;QACf,MAAM,EAAE,EAAE;QACV,IAAI,EAAE,EAAE;QACR,IAAI,EAAE,IAAK;QACX,QAAQ,EAAE,EAAE;KACf,CAAA;IAED,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,CAAM,EAAE,EAAE;QACjC,MAAM,KAAK,GAAI,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAW;aACpE,IAAI,EAAE;aACN,GAAG,CAAC,CAAC,CAAC,EAAE;YACL,MAAM,QAAQ,GAAI,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,CAAW,CAAC,GAAG,CAAC,CAAC,CAAM,EAAE,EAAE;gBACxF,MAAM,CAAC,GAAG,CAAC,CAAC,MAAM,CAAA;gBAClB,MAAM,OAAO,GAAY;oBACrB,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,UAAU,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,UAAU,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,UAAU,CAAC,QAAQ,EAAE,GAAG,CAAC;oBAC5I,IAAI,EAAE,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC;oBACrD,UAAU,EAAE,CAAC,CAAC,CAAC,EAAE;iBACpB,CAAA;gBACD,IAAI,OAAO,CAAC,UAAU,GAAG,aAAa,EAAE,CAAC;oBACrC,OAAO,IAAK,CAAA;gBAChB,CAAC;gBACD,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;gBAC3B,OAAO,OAAO,CAAA;YAClB,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAA;YAEjB,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;gBACnB,OAAO,IAAK,CAAA;YAChB,CAAC;YAED,MAAM,IAAI,GAAS;gBACf,IAAI,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;gBACzC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;gBAC1C,KAAK,EAAE,QAAQ;aAClB,CAAA;YACD,OAAO,IAAI,CAAA;QACf,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAA;QAErB,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;YAChB,OAAO,IAAK,CAAA;QAChB,CAAC;QAED,MAAM,KAAK,GAAU;YACjB,IAAI,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;YACvC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;YACvC,KAAK,EAAE,KAAK;SACf,CAAA;QACD,OAAO,KAAK,CAAA;IAChB,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAA;IACjB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAA;IACnD,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;IAEnD,OAAO,IAAI,CAAA;AACf,CAAC;AAwBD,MAAM,IAAI;IAEK;IACA;IACA;IACA;IAJX,YACW,CAAS,EACT,CAAS,EACT,KAAa,EACb,MAAc;QAHd,MAAC,GAAD,CAAC,CAAQ;QACT,MAAC,GAAD,CAAC,CAAQ;QACT,UAAK,GAAL,KAAK,CAAQ;QACb,WAAM,GAAN,MAAM,CAAQ;IACzB,CAAC;IAED,IAAI,CAAC,KAAW;QACZ,OAAO,IAAI,IAAI,CACX,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,EACzB,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,EACzB,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,EAChF,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CACrF,CAAA;IACL,CAAC;IAED,MAAM,CAAC,IAAI,CAAC,KAAa;QACrB,OAAO,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAA;IACtF,CAAC;CACJ"}