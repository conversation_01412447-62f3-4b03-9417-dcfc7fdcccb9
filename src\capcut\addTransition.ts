import { TemplateProject, templateProject } from "./templateProject"
import { getEffectPath, uuid } from "../libs/utils"


export const addTransition = async (project: TemplateProject, videoIndex: number, transitionResourceId: string, duration: number, trackIndex: number) => {
    duration = Math.floor(duration * 1_000_000)
    const effectPath = await getEffectPath(transitionResourceId)
    const track = project.tracks[trackIndex]
    if (track.type !== 'video') {
        throw new Error(`Invalid track type "${track.type}" to add transition, must be "video"`)
    }
    if (videoIndex >= track.segments.length - 1) {
        throw new Error(`Invalid video index ${videoIndex} to add transition, must be less than ${track.segments.length - 1}. Transition must be between 2 videos.`)
    }
    const video = track.segments[videoIndex]

    const transition = templateProject().materials.transitions[0]
    transition.id = uuid()
    transition.duration = Math.round(duration / project.fps * (project.fps - 2))
    transition.effect_id = transitionResourceId
    transition.path = effectPath
    transition.resource_id = transitionResourceId
    transition.third_resource_id = transitionResourceId
    project.materials.transitions.push(transition)

    video.extra_material_refs.push(transition.id as never)
}
