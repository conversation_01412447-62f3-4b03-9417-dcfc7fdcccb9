import path from 'path'
import pfs from 'fs/promises'
import fs  from 'fs'
import { exec } from 'child_process'
import { CapCutEffectDir } from '../capcut/emptyProject'
import crypto from 'crypto'
import ffmpeg from 'fluent-ffmpeg'
import type { FfprobeData } from 'fluent-ffmpeg'

export const uuid = () => crypto.randomUUID().toUpperCase()

export const killProcess = async (name: string) => {
    try {
        await new Promise<void>((resolve, reject) => {
            exec(`taskkill /F /IM ${name}`, (error, stdout, stderr) => {
                if (error) {
                    reject(error);
                } else {
                    resolve();
                }
            });
        });
        console.log(`${name} process killed successfully`);
    } catch (error: any) {
        if (error.message.includes('not found')) {
            return
        }
        console.error(`${error}`);
    }
}

export const getEffectPath = async (effectId: string) => {
    const r = path.join(CapCutEffectDir, effectId)
    let dir = (await pfs.readdir(r)).filter(d => {
        const p = path.join(r, d)
        //  check if p is a directory
        return fs.statSync(p).isDirectory()
    })[0]

    if (dir) {
        dir = path.join(r, dir)
    } else {
        dir = r
    }
    return dir
}


export const getProbe = (mediaPath: string): Promise<FfprobeData> => {
    return new Promise((resolve, reject) => {
        ffmpeg.ffprobe(mediaPath, (err, data) => {
            if (err) {
                reject(err)
            } else {
                resolve(data)
            }
        })
    })
}

export const Utils = {
    shuffleArray(array: any[]) {
        for (let i = array.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [array[i], array[j]] = [array[j], array[i]];
        }
        return array
    },
    sleep(timeOut: number) : Promise<void> {
        return new Promise<void>(resolve => {
            setTimeout(resolve, timeOut);
        })
    },
    randomString(length: number) : string {
        if (length < 1) {
            throw new Error('Invalid parameter length < 1')
        }
        let chars = normalChars;
        return new Array(length).fill('0').map(_ => chars[Math.random() * chars.length | 0]).join('')
    },
    randomOtp(length: number) {
        return new Array(length)
            .fill('0')
            .map(_ => normalChars[10 + Math.random() * 26 | 0])
            .join('')
    },
    async mapAsync<T, V>(array: Array<T>, 
        mapper: (elem: T, index: number, array: Array<T>) => Promise<V>, 
        concurrency = 0)
    {
        concurrency = concurrency | 0
        concurrency = concurrency <= 0 ? array.length : Math.min(concurrency, array.length)
        let index = 0
        const results: V[] = []

        const worker = async () => {
            while (index < array.length) {
                const i = index++
                const r = await mapper(array[i], i, array)
                results[i] = r
            }
        }
        let concurrent = new Array(concurrency)
        concurrent.fill(0)
        await Promise.all(concurrent.map(_ => worker()))
        return results
    },
    async runSequence<T>(key: any,
        runner: () => Promise<T>, 
        timeStepMs: number = 0, 
        maxTimeStepMs: number = 0, 
        distribution: number = 1): Promise<T> 
    {
        let this_ = Utils as any
        let cache: {[k in string]: {start: number, done: boolean} | undefined}
            = this_.runCache = this_.runCache || {}
        let pending = cache[key]
        if (!pending) {
            let result: T
            pending = cache[key] = {start: Date.now(), done: false}
            try {
                result = await runner()
            } finally {
                pending.done = true
                // delete cache[key] don't uncomment this line, deleted key cannot check time
            }
            return result
        }
        if (timeStepMs > 0 && maxTimeStepMs > timeStepMs && distribution >= 0) {
            timeStepMs += Math.round(
                Math.random()
                * (maxTimeStepMs - timeStepMs)
                * (Math.random() < distribution ? 1 : distribution)
            )
        }
        while (true) {
            pending = cache[key]
            if (!pending) break

            let delayTime = timeStepMs - (Date.now() - pending.start)
            if (!pending.done || delayTime > 0) {
                await Utils.sleep(Utils.clamp(delayTime, 50, 1000))
            } else {
                delete cache[key] // safe to delete as done and passed time step
                break
            }
        }
        return await Utils.runSequence(key, runner, timeStepMs)
    },
    clamp(value: number, min: number, max: number) {
        return value < min ? min : value > max ? max : value
    },
    chalk: {
        foregroundColors: {
            'black': '\x1b[30m',
            'red': '\x1b[31m',
            'green': '\x1b[32m',
            'yellow': '\x1b[33m',
            'blue': '\x1b[34m',
            'magenta': '\x1b[35m',
            'cyan': '\x1b[36m',
            'white': '\x1b[37m',
            'bright-black': '\x1b[90m',
            'bright-red': '\x1b[91m',
            'bright-green': '\x1b[92m',
            'bright-yellow': '\x1b[93m',
            'bright-blue': '\x1b[94m',
            'bright-magenta': '\x1b[95m',
            'bright-cyan': '\x1b[96m',
            'bright-white': '\x1b[97m',
            'none': '\x1b[0m'
        } as const,
        backgroundColors: {
            'black': '\x1b[40m',
            'red': '\x1b[41m',
            'green': '\x1b[42m',
            'yellow': '\x1b[43m',
            'blue': '\x1b[44m',
            'magenta': '\x1b[45m',
            'cyan': '\x1b[46m',
            'white': '\x1b[47m',
            'bright-black': '\x1b[100m',
            'bright-red': '\x1b[101m',
            'bright-green': '\x1b[102m',
            'bright-yellow': '\x1b[103m',
            'bright-blue': '\x1b[104m',
            'bright-magenta': '\x1b[105m',
            'bright-cyan': '\x1b[106m',
            'bright-white': '\x1b[107m',
        } as const,
        /**
         * Make rainbow text to print
         * @link https://en.m.wikipedia.org/wiki/ANSI_escape_code#Colors
         * @param text text to transform
         * @param foreground if foreground is provided, make rainbow in background
         * @returns transformed rainbow text
         */
        rainbow(text: string, foreground?: 'black' | 'red' | 'green' | 'yellow' | 'blue' | 'magenta' | 'cyan' | 'white') {
            const foregroundCode = foreground ? {
                'black': '\x1b[30m',
                'red': '\x1b[91m',
                'green': '\x1b[32m',
                'yellow': '\x1b[93m',
                'blue': '\x1b[34m',
                'magenta': '\x1b[35m',
                'cyan': '\x1b[36m',
                'white': '\x1b[97m',
            }[foreground] : ''
            const l = text.length
            const colors = foreground ?
                ['\x1b[41m', '\x1b[43m', '\x1b[102m', '\x1b[106m', '\x1b[104m', '\x1b[105m'].map(c => c + foregroundCode).concat(['\x1b[0m']) :
                ['\x1b[31m', '\x1b[33m', '\x1b[92m', '\x1b[96m', '\x1b[94m', '\x1b[95m', '\x1b[0m']
            const step = l / (colors.length - 1)
            const indexArr = new Array(colors.length).fill(0)
            for (let i = 1; i < colors.length; i++) {
                indexArr[i] = Math.round(i * step)
            }
            let result = colors[0]
            for (let i = 1; i < colors.length; i++) {
                result += text.substring(indexArr[i - 1], indexArr[i])
                result += colors[i]
            }
            return result
        },
        /**
         * Make rainbow text that loop to print
         * @link https://en.m.wikipedia.org/wiki/ANSI_escape_code#Colors
         * @param text text to transform
         * @param foreground if foreground is provided, make rainbow in background
         * @returns transformed rainbow text
         */
        rainbow2(text: string, foreground?: 'black' | 'red' | 'green' | 'yellow' | 'blue' | 'magenta' | 'cyan' | 'white', minGroup: number = 3) {
            const foregroundCode = foreground ? {
                'black': '\x1b[30m',
                'red': '\x1b[91m',
                'green': '\x1b[32m',
                'yellow': '\x1b[93m',
                'blue': '\x1b[34m',
                'magenta': '\x1b[35m',
                'cyan': '\x1b[36m',
                'white': '\x1b[97m',
            }[foreground] : ''
            const colors = foreground ?
                ['\x1b[41m', '\x1b[43m', '\x1b[102m', '\x1b[106m', '\x1b[104m', '\x1b[105m'].map(c => c + foregroundCode) :
                ['\x1b[31m', '\x1b[33m', '\x1b[92m', '\x1b[96m', '\x1b[94m', '\x1b[95m']
            if (text.length <= colors.length * minGroup) {
                return this.rainbow(text, foreground)
            }
            let direction = 0, i = 0
            let result = text.split('').reduce((prev, curr) => {
                let last = prev[prev.length - 1]
                if (last.length < minGroup) {
                    last += curr
                    prev[prev.length - 1] = last
                } else {
                    prev.push(curr)
                }
                return prev
            }, ['']).map((c) => {
                i += direction
                if (i === 0) direction = 1
                if (i === colors.length - 1) direction = -1
                return colors[i] + c
            }).join('') + '\x1b[0m'
            return result
        },
        paint(text: string, 
            fore?: 'black' | 'red' | 'green' | 'yellow' | 'blue' | 'magenta' | 'cyan' | 'white' | 'bright-black' | 'bright-red' | 'bright-green' | 'bright-yellow' | 'bright-blue' | 'bright-magenta' | 'bright-cyan' | 'bright-white' | 'none',
            back?: 'black' | 'red' | 'green' | 'yellow' | 'blue' | 'magenta' | 'cyan' | 'white' | 'bright-black' | 'bright-red' | 'bright-green' | 'bright-yellow' | 'bright-blue' | 'bright-magenta' | 'bright-cyan' | 'bright-white'
        ) {
            if (fore) {
                text = this.foregroundColors[fore] + text
            }
            if (back) {
                text = this.backgroundColors[back] + text
            }
            if (fore || back) {
                text += '\x1b[0m'
            }
            return text
        }
    } as const
}

const normalChars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz-_'.split('')