import { addTrack } from './addTrack'
import { templateProject, templateSegment } from './templateProject'
import { uuid } from '../libs/utils'

export const addSubtitle = (project: ReturnType<typeof import('./templateProject').templateProject>, content: string, start: number, duration: number, trackIndex: number) => {
    start = Math.floor(start * 1_000_000)
    duration = Math.floor(duration * 1_000_000)
    
    const materials = project.materials

    const materialAnimation = templateProject().materials.material_animations[0]
    materialAnimation.id = uuid()
    materials.material_animations.push(materialAnimation)

    const text = templateProject().materials.texts[0]
    text.id = uuid()
    const contentObj = JSON.parse(text.content)
    contentObj.text = content
    contentObj.styles[0].range = [0, content.length]
    text.content = JSON.stringify(contentObj)
    materials.texts.push(text)

    const track = addTrack(project, "text", trackIndex)
    const segment = templateSegment("text")
    segment.id = uuid()
    segment.extra_material_refs = [materialAnimation.id]
    segment.material_id = text.id
    segment.target_timerange = {
        start: start,
        duration: duration
    }
    track.segments.push(segment as any)
    
    project.duration = Math.max(project.duration, start + duration)

}