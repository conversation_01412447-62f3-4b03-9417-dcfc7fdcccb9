import path from 'path'
import { TemplateProject, templateProject, templateSegment } from './templateProject'
import { getProbe, uuid } from '../libs/utils'
import { Jimp } from 'jimp'
import { addTrack } from "./addTrack"

/**
 * @param {TemplateProject} project
 * @param {string} clipPath image or video path
 * @param {number} start -1 to append to track.
 * @param {number} duration
 * @param {number} trackIndex
 */
export const addClip = async (project: TemplateProject, clipPath: string, start: number, startSrc: number, duration: number, trackIndex: number) => {
    const isImage = ['.jpg', '.png', '.jpeg', '.gif', '.bmp'].some(ext => clipPath.endsWith(ext))
    const isVideo = ['.mp4', '.mov', '.avi', '.mkv'].some(ext => clipPath.endsWith(ext))
    if (!isImage && !isVideo) {
        throw new Error(`Invalid clip path ${clipPath}. Only support image and video.`)
    }
    duration = Math.floor(duration * 1_000_000)
    const materials = project.materials

    const canvasId = uuid()
    const canvas = templateProject().materials.canvases[0]
    canvas.id = canvasId
    materials.canvases.push()

    const placeholderId = uuid()
    const placeholder = templateProject().materials.placeholder_infos[0]
    placeholder.id = placeholderId
    materials.placeholder_infos.push(placeholder)

    const soundChannelMappingId = uuid()
    const soundChannelMapping = templateProject().materials.sound_channel_mappings[0]
    soundChannelMapping.id = soundChannelMappingId
    materials.sound_channel_mappings.push(soundChannelMapping)

    const speedId = uuid()
    const speed = templateProject().materials.speeds[0]
    speed.id = speedId
    materials.speeds.push(speed)

    const videoId = uuid()
    const video = templateProject().materials.videos[0]
    video.id = videoId
    video.path = clipPath
    video.material_name = path.basename(clipPath)
    video.category_name = ''
    video.local_material_id = ''
    if (isImage) {
        const jImage = await Jimp.read(clipPath)
        video.duration = duration
        video.width = jImage.width
        video.height = jImage.height
        video.type = 'photo'
    } else {
        const probe = await getProbe(clipPath)
        video.duration = Math.floor(probe.format.duration! * 1_000_000)
        video.width = probe.streams[0].width!
        video.height = probe.streams[0].height!
        video.type = 'video'
        video.has_audio = true
    }
    materials.videos.push(video)

    const vocalSeparationId = uuid()
    const vocalSeparation = templateProject().materials.vocal_separations[0]
    vocalSeparation.id = vocalSeparationId
    materials.vocal_separations.push(vocalSeparation)

    const track = addTrack(project, "video", trackIndex)
    if (start < 0) {
        start = 0
        // append
        const lastSegment = track.segments[track.segments.length - 1]
        if (lastSegment) {
            start = lastSegment.target_timerange.start + lastSegment.target_timerange.duration
        }
    } else {
        start = Math.floor(start * 1_000_000)
    }

    const segment = isImage ? templateSegment("video", "still") : templateSegment("video")
    segment.id = uuid()
    segment.material_id = videoId
    segment.extra_material_refs = [canvasId, placeholderId, soundChannelMappingId, speedId, vocalSeparationId]
    segment.source_timerange = {
        start: startSrc,
        duration: duration
    }
    segment.target_timerange = {
        start: start,
        duration: duration
    }
    segment.common_keyframes.length = 0
    track.segments.push(segment as any)

    project.duration = Math.max(project.duration, start + duration)

    return {segment, video}
}
