import path from "path"
import fs from "fs"
import pfs from "fs/promises"
import os from "os"
import { addTrack } from "./addTrack"
import { CapCutEffectDir } from "./emptyProject"
import { TemplateProject, templateProject, templateSegment } from "./templateProject"
import { getEffectPath, uuid } from "../libs/utils"
import { EffectId, setEffect } from "./effects"

export const addEffect = async (project: TemplateProject, effectResourceId: EffectId, start: number, duration: number, trackIndex: number = 0): Promise<TemplateProject['materials']['video_effects'][0]> => {
    start = Math.floor(start * 1_000_000)
    duration = Math.floor(duration * 1_000_000)
    const effectPath = await getEffectPath(effectResourceId)

    const videoEffect = templateProject().materials.video_effects[0]
    videoEffect.id = uuid()
    videoEffect.resource_id = effectResourceId
    videoEffect.effect_id = effectResourceId
    videoEffect.path = effectPath
    videoEffect.name = "effect " + (project.materials.video_effects.length + 1)
    videoEffect.adjust_params = []
    const extra = path.join(effectPath, "extra.json")
    if (fs.existsSync(extra)) {
        const content = fs.readFileSync(extra, "utf8")
        const json = JSON.parse(content)
        videoEffect.adjust_params = json.setting.effect_adjust_params.map((p: any) => ({ default_value: p.default, name: p.effect_key, value: p.default }))
    }
    setEffect(videoEffect, effectResourceId)
    project.materials.video_effects.push(videoEffect)

    const track = addTrack(project, "effect", trackIndex)
    const segment = templateSegment("effect")
    segment.id = uuid()
    segment.material_id = videoEffect.id
    segment.target_timerange = {
        start: start,
        duration: duration
    }
    track.segments.push(segment as any)

    project.duration = Math.max(project.duration, start + duration)

    return videoEffect
}

export type EffectNameId = 'color_filter'
export const addEffect0 = async (project: TemplateProject, nameId: EffectNameId, segment: TemplateProject['tracks'][0]['segments'][0], alpha: number): Promise<void> => {
    const defaultDir = path.join(os.homedir(), "AppData", "Local", "CapCut", "Apps")
    const dirs = fs.readdirSync(defaultDir)
        .filter(d => fs.statSync(path.join(defaultDir, d)).isDirectory() && d.split('.').every(x => /^\d+$/g.test(x)))
        .map(d => ({d, v: d.split('.').map(Number)}))
        .sort((a, b) => a.v[0] - b.v[0] || a.v[1] - b.v[1] || a.v[2] - b.v[2] || a.v[3] - b.v[3])
    const dir = path.join(defaultDir, dirs[dirs.length - 1].d, "Resources", "MixMode")
    const mixMode = JSON.parse(await pfs.readFile(path.join(dir, 'MixMode.json'), 'utf8'))
    const effect = mixMode.resourceList.find((e: any) => e.nameId === nameId)
    if (!effect) {
        throw new Error(`Effect ${nameId} not found`)
    }
    const effectData = {
        "adjust_params": [],
        "algorithm_artifact_path": "",
        "apply_target_type": 0,
        "beauty_face_auto_preset_id": "",
        "bloom_params": null,
        "category_id": "",
        "category_name": "",
        "color_match_info": {
            "source_feature_path": "",
            "target_feature_path": "",
            "target_image_path": ""
        },
        "covering_relation_change": 0,
        "effect_id": effect.effectId,
        "enable_skin_tone_correction": false,
        "exclusion_group": [],
        "face_adjust_params": [],
        "formula_id": "",
        "id": uuid(),
        "intensity_key": "",
        "item_effect_type": 0,
        "lumi_hub_path": "",
        "multi_language_current": "",
        "name": "Screen",
        "panel_id": "",
        "path": path.join(dir, effect.path),
        "platform": "all",
        "request_id": "",
        "resource_id": effect.resourceId,
        "source_platform": 0,
        "sub_type": "none",
        "third_resource_id": "",
        "time_range": null,
        "type": "mix_mode",
        "value": 1,
        "version": ""
    }
    project.materials.effects.push(effectData as never)
    segment.clip!.alpha = alpha
    segment.extra_material_refs.push(effectData.id as never)
}