
import os
import sys
from Scripts.audio import join_mp3s, transcribe

# Read the first argument
argv = sys.argv
print(argv)
# argv = ['1', 'C:\\Users\\<USER>\\OneDrive\\YoutubeContent\\The Last Straw Stories\\My Mother-in-Law Rewrote My Wedding Promises in Secret—My Words at the Altar Made Her Rethink Everything']
if len(argv) > 1:
    assets_dir = argv[1]
    join_mp3s(assets_dir)
    mp3_files = [f for f in os.listdir(assets_dir) if f.lower().endswith('.mp3')]
    if not mp3_files:
        # raise Exception("No MP3 file found in the folder.")
        sys.exit(2)
    if len(mp3_files) > 1:
        # raise Exception("More than one MP3 file found in the folder.")
        sys.exit(3)
    mp3_path = os.path.join(assets_dir, mp3_files[0])
    subs = transcribe(mp3_path)
    sys.exit(0)
else:
    sys.exit(1)