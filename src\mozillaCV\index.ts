import pfs from 'fs/promises'
import path from 'path'
import fs from 'fs'
import fetch from 'node-fetch'
import { MozillaCVClip } from './types'
import { tsvToJson } from '../libs/tsv'
import { randomName } from './names'

async function main() {
    const argv = process.argv.slice(2)
    console.log("ARGV", argv)
    const dirs = await findDir(argv[0])
    const db: MozillaCVClip[] = []
    for (let dir of dirs) {
        await readDB(dir, db)
        console.log("DB", db.length, dir)
    }

    const result = db.filter(x => (x.accents || '').includes('United States English'))
    console.log("RESULT", result.length, result)
}

async function findDir(root: string, dirs: string[] = []) {
    const files = await pfs.readdir(root)
    if (files.some(x => x === 'validated.tsv')) {
        dirs.push(root)
    }
    for (let file of files) {
        const p = path.join(root, file)
        if (fs.statSync(p).isDirectory()) {
            await findDir(p, dirs)
        }
    }
    return dirs
}

async function readDB(dir: string, db: MozillaCVClip[] = []) {
    // const language = path.basename(dir)
    const tsvPath = path.join(dir, 'validated.tsv')
    const data: MozillaCVClip[] = await tsvToJson(tsvPath)
    data.forEach(x => {
        // x.language = language
        x.up_votes = +x.up_votes
        x.down_votes = +x.down_votes
        x.gender = x.gender.split('_')[0] as MozillaCVClip['gender']
        x.name = randomName(x.gender as MozillaCVClip['gender'], x.client_id)
        delete (x as any).client_id
        delete (x as any).sentence_id
        for (const k in x) {
            if (x[k as keyof typeof x] === '') {
                delete (x as any)[k]
            }
        }
        db.push(x)
    })
    return db
}

async function TestFetch() {
    try {
        const params = {
            text: `I was wide awake when it happened.
Not from caffeine.
Not from nerves.
But from pain—the kind that lingers after surgery.
Just three days ago, doctors removed a tumor from my abdomen. I was recovering at home, alone in the quiet, with nothing but soft lighting, the hum of a white noise machine, and a stubborn ache that refused to let me sleep.`,
            text_lang: 'en',
            ref_audio_path: "C:\\MyData\\refAudio.mp3",
            prompt_text: "I invited you to my Halloween party specifically this morning and you aren't here, and that's really rude.",
            prompt_lang: 'en',
            // media_type: 'aac',
        }
        const response = await fetch('http://localhost:9880/tts?' + new URLSearchParams(params));
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        const data = await response.arrayBuffer();
        fs.writeFileSync('test.wav', Buffer.from(data))
        console.log('Fetched data:', data);
    } catch (error) {
        console.error('Error fetching data:', error);
    }
}

TestFetch()

// main()