
@echo off

REM Check if RESOLVE_SCRIPT_API is already set
if defined RESOLVE_SCRIPT_API (
    echo RESOLVE_SCRIPT_API is already set to: %RESOLVE_SCRIPT_API%
    echo Exiting...
    exit /b 0
)

REM Set the path to DaVinci Resolve's Scripting API directory
set RESOLVE_SCRIPT_API=%PROGRAMDATA%\Blackmagic Design\DaVinci Resolve\Support\Developer\Scripting
setx RESOLVE_SCRIPT_API "%RESOLVE_SCRIPT_API%"

REM Set the path to DaVinci Resolve's Fusion Script DLL.
REM                    NOTE: WHERE YOU INSTALLED DaVinciResolve?
set RESOLVE_SCRIPT_LIB=C:\Program Files\Blackmagic Design\DaVinci Resolve\fusionscript.dll
setx RESOLVE_SCRIPT_LIB "%RESOLVE_SCRIPT_LIB%"

REM Add the Modules directory to Python's path for importing Resolve modules
set PYTHONPATH=%PYTHONPATH%;%RESOLVE_SCRIPT_API%\Modules
setx PYTHONPATH "%PYTHONPATH%"

REM Display the updated PYTHONPATH
echo %PYTHONPATH%