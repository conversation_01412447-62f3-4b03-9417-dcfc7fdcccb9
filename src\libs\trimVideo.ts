import ffmpeg from 'fluent-ffmpeg'
import path from 'path'
import pfs from 'fs/promises'
import fs from 'fs'
import { exec } from 'child_process'
import { Writable } from 'stream'
import { getProbe } from './utils'
import { findVideoClips } from '../tool'

const errorDir = path.resolve(path.join('.', 'output', 'ffmpeg-stderr'))

async function sleep(timeOut: number) {
    return new Promise(resolve => {
        setTimeout(resolve, timeOut);
    })
}

const blackDetectOptions = 'blackdetect=d=0.001:pic_th=0.98'

// Parse ffmpeg stderr to find black frame data
function parseBlackFrames(stderr: string) {
    const regex = /black_start:(\d+(\.\d+)?)\s+black_end:(\d+(\.\d+)?)/g
    const matches = [...stderr.matchAll(regex)]

    const blackFrames: [number, number][] = []
    for (const match of matches) {
        blackFrames.push([parseFloat(match[1]), parseFloat(match[3])])
    }

    return blackFrames
}

// Detect black frames
async function detectBlackFrames(filePath: string) {
    if (!fs.existsSync(errorDir)) {
        await pfs.mkdir(errorDir, { recursive: true })
    }
    return new Promise<{ blackFrames: [number, number][], duration: any }>((resolve, reject) => {
        const filename = path.basename(filePath).split('.')[0].replaceAll(/\W/g, '').slice(0, 30)
        const stderrFile = path.join(errorDir, `blacks_${filename}_${Math.random().toString().substring(2, 9)}.txt`)
        exec(`ffmpeg -i "${filePath}" -vf ${blackDetectOptions} -f null - 2>${stderrFile}`, async (error) => {
            if (error) {
                reject(error)
            } else {
                const stderr = await pfs.readFile(stderrFile, 'utf8')
                const probes = await getProbe(filePath)
                const duration = probes.format.duration!
                const blackFrames = parseBlackFrames(stderr)
                resolve({duration, blackFrames })
            }
        });
    })
}

// Trim video
function trimVideo(inputPath: string, outputPath: string | Writable, startTrim: number, endTrim: any, duration: any) {
    return new Promise<void>((resolve, reject) => {
        const endTime = endTrim || duration;
        const length = endTime - startTrim;

        ffmpeg(inputPath)
            .setStartTime(startTrim)
            .setDuration(length)
            .output(outputPath)
            .on('end', () => resolve())
            .on('error', reject)
            .run()
    })
}

// Main batch processor
export async function trimVideoInBatch(assetDir: string) {
    const { videos } = await findVideoClips(assetDir)

    const outputDir = path.dirname(videos[0])

    for (const file of videos) {
        const filename = path.basename(file).split('.')
        const outPath = path.join(outputDir, filename[0] + '_trimmed.' + filename[1])
        console.log(`Processing ${filename.join('.')}...`)
        try {
            await sleep(100)
            const { blackFrames, duration } = await runTillSuccess(() => detectBlackFrames(file))
            if (blackFrames.length) {
                if (blackFrames.length === 1) {
                    if (Math.abs(duration - blackFrames[0][1]) < Math.abs(duration - blackFrames[0][0])) {
                        blackFrames.push([duration, duration])
                    } else {
                        blackFrames.unshift([0, 0])
                    }
                }
                const startTrim = blackFrames[0][1]
                const endTrim = blackFrames[blackFrames.length - 1][0]
                console.log(`➤ Trimming from ${startTrim}s to ${endTrim || duration}s`)
                await trimVideo(file, outPath, startTrim, endTrim, duration)
                await sleep(100)
                await pfs.rm(file, { force: true })
                await sleep(100)
                await pfs.rename(outPath, file)
                console.log(`✅ Saved to ${file}`)
            }
        } catch (err: any) {
            console.error(`❌ Error processing ${file}:`, err.message)
        }
    }
}

async function runTillSuccess<T>(fn: () => Promise<T>, maxRetries = 3, retryDelay = 200) {
    let retries = 0
    while (retries < maxRetries) {
        try {
            return await fn()
        } catch (err: any) {
            console.error(`❌ Error running ${fn.name}:`, err.message)
            retries++
            if (retries < maxRetries) {
                console.log(`Retrying in ${retryDelay}ms...`)
                await sleep(retryDelay)
            }
        }
    }
    throw new Error(`Failed to run ${fn.name} after ${maxRetries} retries`)
}

async function findChangeScenePosition(filePath: string) {
    if (!fs.existsSync(errorDir)) {
        await pfs.mkdir(errorDir, { recursive: true })
    }
    // ffmpeg -i "" -filter:v "select='gt(scene,0.4)',showinfo" -f null - 2> ffoutput.txt
    return new Promise<{ changeScenes: number[] }>((resolve, reject) => {
        const filename = path.basename(filePath).split('.')[0].replaceAll(/\W/g, '').slice(0, 30)
        const stderrFile = path.join(errorDir, `scenes_${filename}_${Math.random().toString().substring(2, 9)}.txt`)
        exec(`ffmpeg -i "${filePath}" -filter:v "select='gt(scene,0.5)',showinfo" -f null - 2>${stderrFile}`, async (error) => {
            if (error) {
                reject(error)
            } else {
                const probes = await getProbe(filePath)
                const duration = probes.format.duration!
                const stderr = await pfs.readFile(stderrFile, 'utf8')
                const changeScenes = parseChangeScene(stderr)
                changeScenes.push(duration)
                resolve({ changeScenes })
            }
        });
    })
}

function parseChangeScene(stderr: string) {
    const regex = /pts_time:(\d+\.\d+)/g
    const matches = [...stderr.matchAll(regex)]

    const changeScenes: number[] = [0]
    for (const match of matches) {
        changeScenes.push(parseFloat(match[1]))
    }

    return changeScenes
}

export async function splitVideoByScene(inputPath: string) {
    const outputDir = path.dirname(inputPath)
    const filename = path.basename(inputPath).split('.')
    console.time('findChangeScenePosition ' + filename[0])
    const { changeScenes } = await findChangeScenePosition(inputPath)
    console.timeEnd('findChangeScenePosition ' + filename[0])
    for (let i = 1; i < changeScenes.length; i++) {
        const start = changeScenes[i - 1]
        const end = changeScenes[i]
        if (end - start < 4) {
            continue
        }
        const outputName = filename[0] + `_${i}.` + filename[1]
        const outputPath = path.join(outputDir, outputName)
        if (fs.existsSync(outputPath)) {
            continue
        }
        const label = `trimVideo ${outputName}[${start}-${end}]`
        console.time(label)
        await trimVideo(inputPath, outputPath, start + 0.1, end - 0.1, 0)
        console.timeEnd(label)
    }
}
