{"version": 3, "file": "trimVideo.js", "sourceRoot": "", "sources": ["../../src/libs/trimVideo.ts"], "names": [], "mappings": ";;;;;AAuEA,4CAkCC;AAsDD,8CAsBC;AArLD,kEAAkC;AAClC,gDAAuB;AACvB,2DAA6B;AAC7B,4CAAmB;AACnB,iDAAoC;AAEpC,mCAAkC;AAClC,kCAAwC;AAExC,MAAM,QAAQ,GAAG,cAAI,CAAC,OAAO,CAAC,cAAI,CAAC,IAAI,CAAC,GAAG,EAAE,QAAQ,EAAE,eAAe,CAAC,CAAC,CAAA;AAExE,KAAK,UAAU,KAAK,CAAC,OAAe;IAChC,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE;QACzB,UAAU,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IACjC,CAAC,CAAC,CAAA;AACN,CAAC;AAED,MAAM,kBAAkB,GAAG,iCAAiC,CAAA;AAE5D,+CAA+C;AAC/C,SAAS,gBAAgB,CAAC,MAAc;IACpC,MAAM,KAAK,GAAG,sDAAsD,CAAA;IACpE,MAAM,OAAO,GAAG,CAAC,GAAG,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAA;IAE3C,MAAM,WAAW,GAAuB,EAAE,CAAA;IAC1C,KAAK,MAAM,KAAK,IAAI,OAAO,EAAE,CAAC;QAC1B,WAAW,CAAC,IAAI,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;IAClE,CAAC;IAED,OAAO,WAAW,CAAA;AACtB,CAAC;AAED,sBAAsB;AACtB,KAAK,UAAU,iBAAiB,CAAC,QAAgB;IAC7C,IAAI,CAAC,YAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;QAC3B,MAAM,kBAAG,CAAC,KAAK,CAAC,QAAQ,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAA;IAClD,CAAC;IACD,OAAO,IAAI,OAAO,CAAqD,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QACvF,MAAM,QAAQ,GAAG,cAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAA;QACzF,MAAM,UAAU,GAAG,cAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,UAAU,QAAQ,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAA;QAC5G,IAAA,oBAAI,EAAC,cAAc,QAAQ,SAAS,kBAAkB,gBAAgB,UAAU,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE;YAChG,IAAI,KAAK,EAAE,CAAC;gBACR,MAAM,CAAC,KAAK,CAAC,CAAA;YACjB,CAAC;iBAAM,CAAC;gBACJ,MAAM,MAAM,GAAG,MAAM,kBAAG,CAAC,QAAQ,CAAC,UAAU,EAAE,MAAM,CAAC,CAAA;gBACrD,MAAM,MAAM,GAAG,MAAM,IAAA,gBAAQ,EAAC,QAAQ,CAAC,CAAA;gBACvC,MAAM,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,QAAS,CAAA;gBACxC,MAAM,WAAW,GAAG,gBAAgB,CAAC,MAAM,CAAC,CAAA;gBAC5C,OAAO,CAAC,EAAC,QAAQ,EAAE,WAAW,EAAE,CAAC,CAAA;YACrC,CAAC;QACL,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAA;AACN,CAAC;AAED,aAAa;AACb,SAAS,SAAS,CAAC,SAAiB,EAAE,UAA6B,EAAE,SAAiB,EAAE,OAAY,EAAE,QAAa;IAC/G,OAAO,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QACzC,MAAM,OAAO,GAAG,OAAO,IAAI,QAAQ,CAAC;QACpC,MAAM,MAAM,GAAG,OAAO,GAAG,SAAS,CAAC;QAEnC,IAAA,uBAAM,EAAC,SAAS,CAAC;aACZ,YAAY,CAAC,SAAS,CAAC;aACvB,WAAW,CAAC,MAAM,CAAC;aACnB,MAAM,CAAC,UAAU,CAAC;aAClB,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC,OAAO,EAAE,CAAC;aAC1B,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC;aACnB,GAAG,EAAE,CAAA;IACd,CAAC,CAAC,CAAA;AACN,CAAC;AAED,uBAAuB;AAChB,KAAK,UAAU,gBAAgB,CAAC,QAAgB;IACnD,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,IAAA,qBAAc,EAAC,QAAQ,CAAC,CAAA;IAEjD,MAAM,SAAS,GAAG,cAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAA;IAEzC,KAAK,MAAM,IAAI,IAAI,MAAM,EAAE,CAAC;QACxB,MAAM,QAAQ,GAAG,cAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;QAC/C,MAAM,OAAO,GAAG,cAAI,CAAC,IAAI,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC,CAAC,GAAG,WAAW,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAA;QAC7E,OAAO,CAAC,GAAG,CAAC,cAAc,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAA;QAClD,IAAI,CAAC;YACD,MAAM,KAAK,CAAC,GAAG,CAAC,CAAA;YAChB,MAAM,EAAE,WAAW,EAAE,QAAQ,EAAE,GAAG,MAAM,cAAc,CAAC,GAAG,EAAE,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAA;YACrF,IAAI,WAAW,CAAC,MAAM,EAAE,CAAC;gBACrB,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBAC3B,IAAI,IAAI,CAAC,GAAG,CAAC,QAAQ,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;wBAClF,WAAW,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAAA;oBAC1C,CAAC;yBAAM,CAAC;wBACJ,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAA;oBAC/B,CAAC;gBACL,CAAC;gBACD,MAAM,SAAS,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;gBACnC,MAAM,OAAO,GAAG,WAAW,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;gBACtD,OAAO,CAAC,GAAG,CAAC,mBAAmB,SAAS,QAAQ,OAAO,IAAI,QAAQ,GAAG,CAAC,CAAA;gBACvE,MAAM,SAAS,CAAC,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAA;gBAC5D,MAAM,KAAK,CAAC,GAAG,CAAC,CAAA;gBAChB,MAAM,kBAAG,CAAC,EAAE,CAAC,IAAI,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAA;gBACnC,MAAM,KAAK,CAAC,GAAG,CAAC,CAAA;gBAChB,MAAM,kBAAG,CAAC,MAAM,CAAC,OAAO,EAAE,IAAI,CAAC,CAAA;gBAC/B,OAAO,CAAC,GAAG,CAAC,cAAc,IAAI,EAAE,CAAC,CAAA;YACrC,CAAC;QACL,CAAC;QAAC,OAAO,GAAQ,EAAE,CAAC;YAChB,OAAO,CAAC,KAAK,CAAC,sBAAsB,IAAI,GAAG,EAAE,GAAG,CAAC,OAAO,CAAC,CAAA;QAC7D,CAAC;IACL,CAAC;AACL,CAAC;AAED,KAAK,UAAU,cAAc,CAAI,EAAoB,EAAE,UAAU,GAAG,CAAC,EAAE,UAAU,GAAG,GAAG;IACnF,IAAI,OAAO,GAAG,CAAC,CAAA;IACf,OAAO,OAAO,GAAG,UAAU,EAAE,CAAC;QAC1B,IAAI,CAAC;YACD,OAAO,MAAM,EAAE,EAAE,CAAA;QACrB,CAAC;QAAC,OAAO,GAAQ,EAAE,CAAC;YAChB,OAAO,CAAC,KAAK,CAAC,mBAAmB,EAAE,CAAC,IAAI,GAAG,EAAE,GAAG,CAAC,OAAO,CAAC,CAAA;YACzD,OAAO,EAAE,CAAA;YACT,IAAI,OAAO,GAAG,UAAU,EAAE,CAAC;gBACvB,OAAO,CAAC,GAAG,CAAC,eAAe,UAAU,OAAO,CAAC,CAAA;gBAC7C,MAAM,KAAK,CAAC,UAAU,CAAC,CAAA;YAC3B,CAAC;QACL,CAAC;IACL,CAAC;IACD,MAAM,IAAI,KAAK,CAAC,iBAAiB,EAAE,CAAC,IAAI,UAAU,UAAU,UAAU,CAAC,CAAA;AAC3E,CAAC;AAED,KAAK,UAAU,uBAAuB,CAAC,QAAgB;IACnD,IAAI,CAAC,YAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;QAC3B,MAAM,kBAAG,CAAC,KAAK,CAAC,QAAQ,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAA;IAClD,CAAC;IACD,qFAAqF;IACrF,OAAO,IAAI,OAAO,CAA6B,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QAC/D,MAAM,QAAQ,GAAG,cAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAA;QACzF,MAAM,UAAU,GAAG,cAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,UAAU,QAAQ,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAA;QAC5G,IAAA,oBAAI,EAAC,cAAc,QAAQ,6DAA6D,UAAU,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE;YAClH,IAAI,KAAK,EAAE,CAAC;gBACR,MAAM,CAAC,KAAK,CAAC,CAAA;YACjB,CAAC;iBAAM,CAAC;gBACJ,MAAM,MAAM,GAAG,MAAM,IAAA,gBAAQ,EAAC,QAAQ,CAAC,CAAA;gBACvC,MAAM,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,QAAS,CAAA;gBACxC,MAAM,MAAM,GAAG,MAAM,kBAAG,CAAC,QAAQ,CAAC,UAAU,EAAE,MAAM,CAAC,CAAA;gBACrD,MAAM,YAAY,GAAG,gBAAgB,CAAC,MAAM,CAAC,CAAA;gBAC7C,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;gBAC3B,OAAO,CAAC,EAAE,YAAY,EAAE,CAAC,CAAA;YAC7B,CAAC;QACL,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAA;AACN,CAAC;AAED,SAAS,gBAAgB,CAAC,MAAc;IACpC,MAAM,KAAK,GAAG,sBAAsB,CAAA;IACpC,MAAM,OAAO,GAAG,CAAC,GAAG,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAA;IAE3C,MAAM,YAAY,GAAa,CAAC,CAAC,CAAC,CAAA;IAClC,KAAK,MAAM,KAAK,IAAI,OAAO,EAAE,CAAC;QAC1B,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;IAC3C,CAAC;IAED,OAAO,YAAY,CAAA;AACvB,CAAC;AAEM,KAAK,UAAU,iBAAiB,CAAC,SAAiB;IACrD,MAAM,SAAS,GAAG,cAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAA;IACzC,MAAM,QAAQ,GAAG,cAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;IACpD,OAAO,CAAC,IAAI,CAAC,0BAA0B,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAA;IACtD,MAAM,EAAE,YAAY,EAAE,GAAG,MAAM,uBAAuB,CAAC,SAAS,CAAC,CAAA;IACjE,OAAO,CAAC,OAAO,CAAC,0BAA0B,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAA;IACzD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QAC3C,MAAM,KAAK,GAAG,YAAY,CAAC,CAAC,GAAG,CAAC,CAAC,CAAA;QACjC,MAAM,GAAG,GAAG,YAAY,CAAC,CAAC,CAAC,CAAA;QAC3B,IAAI,GAAG,GAAG,KAAK,GAAG,CAAC,EAAE,CAAC;YAClB,SAAQ;QACZ,CAAC;QACD,MAAM,UAAU,GAAG,QAAQ,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAA;QACvD,MAAM,UAAU,GAAG,cAAI,CAAC,IAAI,CAAC,SAAS,EAAE,UAAU,CAAC,CAAA;QACnD,IAAI,YAAE,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE,CAAC;YAC5B,SAAQ;QACZ,CAAC;QACD,MAAM,KAAK,GAAG,aAAa,UAAU,IAAI,KAAK,IAAI,GAAG,GAAG,CAAA;QACxD,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;QACnB,MAAM,SAAS,CAAC,SAAS,EAAE,UAAU,EAAE,KAAK,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC,CAAA;QACjE,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;IAC1B,CAAC;AACL,CAAC"}