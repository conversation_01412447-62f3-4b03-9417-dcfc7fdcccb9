
import screenshot from "screenshot-desktop"
import tesseract from "node-tesseract-ocr"
import { convertXML, createAST } from "simple-xml-to-json"

export async function dumpScreen() {
    try {
        const start = new Date()
        const imgBuf = await screenshot({ format: "png" })
        const scrShot = new Date()
        const text = await tesseract.recognize(imgBuf, {
            lang: 'eng',
            presets: ['alto'],
            binary: '"C:\\Program Files\\Tesseract-OCR\\tesseract.exe"'
        })
        const end = new Date()
        console.log(`Total: ${end.getTime() - start.getTime()}ms`)
        console.log(`Screenshot: ${scrShot.getTime() - start.getTime()}ms`)
        console.log(`OCR: ${end.getTime() - scrShot.getTime()}ms`)
        return packTesseractData(convertXML(text), 0.5)
    } catch (err) {
        console.error("[ERROR]", err)
    }
}

function packTesseractData(json: any, minConfidence = 0.01) {
    const printSpace = json.alto.children.find((c: any) => c.Layout)!
        .Layout.children.find((c: any) => c.Page)!
        .Page.children.find((c: any) => c.PrintSpace)!
        .PrintSpace.children
    const cBlocks = printSpace.map((c: any) => c.ComposedBlock).filter((x: any) => x) as any[]
    const page: Page = {
        blocks: [],
        text: '',
        rect: null!,
        elements: []
    }

    page.blocks = cBlocks.map((c: any) => {
        const lines = (c.children.map((d: any) => d.TextBlock.children) as any[])
            .flat()
            .map(l => {
                const elements = (l.TextLine.children.filter((d: any) => d.String) as any[]).map((d: any) => {
                    const s = d.String
                    const element: Element = {
                        text: s.CONTENT.replaceAll('&gt;', '>').replaceAll('&lt;', '<').replaceAll('&amp;', '&').replaceAll('&quot;', '"').replaceAll('&apos;', "'"),
                        rect: new Rect(+s.HPOS, +s.VPOS, +s.WIDTH, +s.HEIGHT),
                        confidence: +s.WC
                    }
                    if (element.confidence < minConfidence) {
                        return null!
                    }
                    page.elements.push(element)
                    return element
                }).filter(x => x)

                if (!elements.length) {
                    return null!
                }

                const line: Line = {
                    text: elements.map(e => e.text).join(' '),
                    rect: Rect.join(elements.map(e => e.rect)),
                    words: elements
                }
                return line
            }).filter(x => x)

        if (!lines.length) {
            return null!
        }

        const block: Block = {
            text: lines.map(l => l.text).join('\n'),
            rect: Rect.join(lines.map(e => e.rect)),
            lines: lines
        }
        return block
    }).filter(x => x)
    page.rect = Rect.join(page.blocks.map(e => e.rect))
    page.text = page.blocks.map(b => b.text).join('\n')

    return page
}

interface Page extends Text {
    elements: Element[]
    blocks: Block[]
}

interface Block extends Text {
    lines: Line[]
}

interface Line extends Text {
    words: Element[]
}

interface Element extends Text {
    confidence: number
}

interface Text {
    text: string
    rect: Rect
}

class Rect {
    constructor(
        public x: number,
        public y: number,
        public width: number,
        public height: number) {
    }

    join(other: Rect) {
        return new Rect(
            Math.min(this.x, other.x),
            Math.min(this.y, other.y),
            Math.max(this.x + this.width, other.x + other.width) - Math.min(this.x, other.x),
            Math.max(this.y + this.height, other.y + other.height) - Math.min(this.y, other.y)
        )
    }

    static join(rects: Rect[]) {
        return rects.reduce((acc, cur) => acc.join(cur), rects[0] || new Rect(0, 0, 0, 0))
    }
}