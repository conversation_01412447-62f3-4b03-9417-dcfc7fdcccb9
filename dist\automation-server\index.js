"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.startServer = startServer;
const ws_1 = __importDefault(require("ws"));
const machines = new Map();
function startServer() {
    const wss = new ws_1.default.Server({ port: 3456 });
    wss.on('connection', (ws) => {
        let machine = undefined;
        ws.on('message', (message) => {
            const data = JSON.parse(message.toString('utf-8'));
            if (data.opCode === 'login') {
                machine = data.machine;
                if (machine) {
                    machines.set(machine, new Machine(machine, ws));
                }
                else {
                    console.error('No machine data to login', data);
                }
            }
            else if (machine) {
                machines.get(machine)?.onMessage(data);
            }
        });
        ws.on('close', () => {
            if (machine) {
                machines.delete(machine);
            }
            machine = undefined;
        });
    });
}
class Machine {
    id;
    ws;
    browsers = [];
    constructor(id, ws) {
        this.id = id;
        this.ws = ws;
    }
    onMessage(data) {
        const opCode = data.opCode;
        if (this[opCode]) {
            try {
                this[opCode](data);
            }
            catch (e) {
                console.error('Handle message', opCode, e);
            }
        }
        else {
            console.log('Unknown opCode', opCode);
        }
    }
    updateBrowsersInfo(data) {
        this.browsers = data.browsers;
    }
    get activeBrowser() {
        return this.browsers[0];
    }
}
//# sourceMappingURL=index.js.map