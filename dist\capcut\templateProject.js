"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.templateProject = exports.setTemplateFile = exports.templateSegment = void 0;
const fs_1 = __importDefault(require("fs"));
/**
 *
 * @param {"video" | "audio" | "effect" | "text"} type
 * @param {"video" | "still"} subtype type of material video (video, image)
 */
const templateSegment = (type, subtype) => {
    const project = (0, exports.templateProject)();
    const trackByType = project.tracks.find(t => t.type === type);
    if (!trackByType) {
        throw new Error(`Invalid template project, no track found for type ${type}`);
    }
    return trackByType.segments[0];
};
exports.templateSegment = templateSegment;
let templateFromFile;
const setTemplateFile = (file) => {
    if (!file) {
        templateFromFile = undefined;
        return;
    }
    try {
        templateFromFile = JSON.parse(fs_1.default.readFileSync(file, 'utf8'));
    }
    catch (e) {
        console.error(e);
    }
};
exports.setTemplateFile = setTemplateFile;
/**
 * template project construction:
 * Video track: an image
 * Audio track: an audio
 * Effect track: a video effect
 * Text track: subtitle with default text decoration
 */
const templateProject = () => {
    let project = {
        "canvas_config": {
            "background": null,
            "height": 1077,
            "ratio": "original",
            "width": 1920
        },
        "color_space": 0,
        "config": {
            "adjust_max_index": 1,
            "attachment_info": [],
            "combination_max_index": 1,
            "export_range": null,
            "extract_audio_last_index": 1,
            "lyrics_recognition_id": "",
            "lyrics_sync": true,
            "lyrics_taskinfo": [],
            "maintrack_adsorb": true,
            "material_save_mode": 0,
            "multi_language_current": "none",
            "multi_language_list": [],
            "multi_language_main": "none",
            "multi_language_mode": "none",
            "original_sound_last_index": 1,
            "record_audio_last_index": 1,
            "sticker_max_index": 1,
            "subtitle_keywords_config": null,
            "subtitle_recognition_id": "",
            "subtitle_sync": true,
            "subtitle_taskinfo": [],
            "system_font_list": [],
            "use_float_render": false,
            "video_mute": false,
            "zoom_info_params": null
        },
        "cover": null,
        "create_time": 0,
        "duration": 916833333,
        "extra_info": null,
        "fps": 30,
        "free_render_index_mode_on": false,
        "group_container": null,
        "id": "419CB614-FC6E-4276-890A-B5F3E899AE08",
        "is_drop_frame_timecode": false,
        "keyframe_graph_list": [],
        "keyframes": {
            "adjusts": [],
            "audios": [],
            "effects": [],
            "filters": [],
            "handwrites": [],
            "stickers": [],
            "texts": [],
            "videos": []
        },
        "last_modified_platform": {
            "app_id": 359289,
            "app_source": "cc",
            "app_version": "6.1.2",
            "device_id": "7c398101c365c816a7452e58d2e0f482",
            "hard_disk_id": "",
            "mac_address": "7eae0635fa09643462eaf40cd9597a2d,d9aed9fab7102f4f36bd91f70f9b043e,10d2717e7d06a80d409fbb21516ebec0,b6810e741893b764ebb2293d69993b4a",
            "os": "windows",
            "os_version": "10.0.26100"
        },
        "lyrics_effects": [],
        "materials": {
            "ai_translates": [],
            "audio_balances": [],
            "audio_effects": [],
            "audio_fades": [
                {
                    "fade_in_duration": 0,
                    "fade_out_duration": 0,
                    "fade_type": 0,
                    "id": "4906D5EB-4041-42fa-AE0A-19F0FAD7BAC0",
                    "type": "audio_fade"
                }
            ],
            "audio_track_indexes": [],
            "audios": [
                {
                    "ai_music_generate_scene": 0,
                    "ai_music_type": 0,
                    "aigc_history_id": "",
                    "aigc_item_id": "",
                    "app_id": 0,
                    "category_id": "",
                    "category_name": "local",
                    "check_flag": 1,
                    "copyright_limit_type": "none",
                    "duration": 916833333,
                    "effect_id": "",
                    "formula_id": "",
                    "id": "CFCBEF05-3987-40b1-94B3-7BA43CF9C361",
                    "intensifies_path": "",
                    "is_ai_clone_tone": false,
                    "is_ai_clone_tone_post": false,
                    "is_text_edit_overdub": false,
                    "is_ugc": false,
                    "local_material_id": "90fd18c4-60ed-4577-a284-88fc84b162f1",
                    "lyric_type": 0,
                    "moyin_emotion": "",
                    "music_id": "0c5fb247-630e-41c9-a09e-2cc47ed6498c",
                    "music_source": "",
                    "name": "new_At Our Anniversary Feast, My MIL Declared My Replacement—She Was Mistaken.mp3",
                    "path": "C:/Users/<USER>/OneDrive/YoutubeContent/The Last Straw Stories/At Our Anniversary Feast, My MIL Declared My Replacement—She Was Mistaken/new_At Our Anniversary Feast, My MIL Declared My Replacement—She Was Mistaken.mp3",
                    "pgc_id": "",
                    "pgc_name": "",
                    "query": "",
                    "request_id": "",
                    "resource_id": "",
                    "search_id": "",
                    "similiar_music_info": {
                        "original_song_id": "",
                        "original_song_name": ""
                    },
                    "sound_separate_type": "",
                    "source_from": "",
                    "source_platform": 0,
                    "team_id": "",
                    "text_id": "",
                    "third_resource_id": "",
                    "tone_category_id": "",
                    "tone_category_name": "",
                    "tone_effect_id": "",
                    "tone_effect_name": "",
                    "tone_emotion_name_key": "",
                    "tone_emotion_role": "",
                    "tone_emotion_scale": 0,
                    "tone_emotion_selection": "",
                    "tone_emotion_style": "",
                    "tone_platform": "",
                    "tone_second_category_id": "",
                    "tone_second_category_name": "",
                    "tone_speaker": "",
                    "tone_type": "",
                    "tts_generate_scene": "",
                    "tts_task_id": "",
                    "type": "extract_music",
                    "video_id": "",
                    "wave_points": []
                }
            ],
            "beats": [
                {
                    "ai_beats": {
                        "beat_speed_infos": [],
                        "beats_path": "",
                        "beats_url": "",
                        "melody_path": "",
                        "melody_percents": [
                            0
                        ],
                        "melody_url": ""
                    },
                    "enable_ai_beats": false,
                    "gear": 404,
                    "gear_count": 0,
                    "id": "FFB24248-961B-4281-9B4C-FE5707CF93D9",
                    "mode": 404,
                    "type": "beats",
                    "user_beats": [],
                    "user_delete_ai_beats": null
                }
            ],
            "canvases": [
                {
                    "album_image": "",
                    "blur": 0,
                    "color": "",
                    "id": "3BF94175-986B-4ffc-8EBA-2C09B41D478D",
                    "image": "",
                    "image_id": "",
                    "image_name": "",
                    "source_platform": 0,
                    "team_id": "",
                    "type": "canvas_color"
                },
                {
                    "album_image": "",
                    "blur": 0,
                    "color": "",
                    "id": "9EDA9AD9-1501-4503-BE4E-E4E4ED242DA5",
                    "image": "",
                    "image_id": "",
                    "image_name": "",
                    "source_platform": 0,
                    "team_id": "",
                    "type": "canvas_color"
                },
                {
                    "album_image": "",
                    "blur": 0,
                    "color": "",
                    "id": "E50927BC-B349-4216-A773-695C760D9B21",
                    "image": "",
                    "image_id": "",
                    "image_name": "",
                    "source_platform": 0,
                    "team_id": "",
                    "type": "canvas_color"
                }
            ],
            "chromas": [],
            "color_curves": [],
            "common_mask": [],
            "digital_humans": [],
            "drafts": [],
            "effects": [],
            "flowers": [],
            "green_screens": [],
            "handwrites": [],
            "hsl": [
                {
                    "constant_material_id": "105517BB-2A53-4c0f-94F5-93FC78BA7D84",
                    "custom_color": "#FFE64444",
                    "hsl_color_type": 1,
                    "hue": 0,
                    "id": "FB4AEE52-6132-496e-B50D-4B227E1231EE",
                    "interacting": true,
                    "lightness": 0,
                    "lumi_hub_path": "C:/Users/<USER>/AppData/Local/CapCut/Apps/6.1.2.2338/Resources/DefaultAdjustBundle/merge_all_adjust_color/lumi_hub_path",
                    "path": "C:/Users/<USER>/AppData/Local/CapCut/Apps/6.1.2.2338/Resources/DefaultAdjustBundle/merge_all_adjust_color",
                    "resource_id": "",
                    "saturation": 0,
                    "source_platform": 0,
                    "type": "hsl",
                    "version": "1"
                }
            ],
            "images": [],
            "log_color_wheels": [],
            "loudnesses": [
                {
                    "enable": false,
                    "file_id": "",
                    "id": "8CAA68CC-BD5B-430a-AF4F-60A25457DB8B",
                    "loudness_param": null,
                    "target_loudness": 0,
                    "time_range": null
                },
                {
                    "enable": false,
                    "file_id": "",
                    "id": "AA80BF02-C707-4570-BDCC-1F2C4F37BA16",
                    "loudness_param": null,
                    "target_loudness": 0,
                    "time_range": null
                }
            ],
            "manual_beautys": [],
            "manual_deformations": [],
            "material_animations": [
                {
                    "animations": [],
                    "id": "785F460F-A80D-4092-8E8C-0CCEDB4495A3",
                    "multi_language_current": "none",
                    "type": "sticker_animation"
                },
                {
                    "animations": [],
                    "id": "BCED53E8-D5BA-43bf-AFAE-AABE43BEE010",
                    "multi_language_current": "none",
                    "type": "sticker_animation"
                },
                {
                    "animations": [],
                    "id": "061DD99C-12DF-4bf0-A139-3F10923DAE8A",
                    "multi_language_current": "none",
                    "type": "sticker_animation"
                },
                {
                    "animations": [],
                    "id": "265D24D4-589F-4ec4-8E7E-4C59AC916501",
                    "multi_language_current": "none",
                    "type": "sticker_animation"
                }
            ],
            "material_colors": [],
            "multi_language_refs": [],
            "placeholder_infos": [
                {
                    "error_path": "",
                    "error_text": "",
                    "id": "D300E548-B699-4987-81DB-104C609D00E9",
                    "meta_type": "none",
                    "res_path": "",
                    "res_text": "",
                    "type": "placeholder_info"
                },
                {
                    "error_path": "",
                    "error_text": "",
                    "id": "5F32E864-AC12-4155-8020-BEEDB0DD96CA",
                    "meta_type": "none",
                    "res_path": "",
                    "res_text": "",
                    "type": "placeholder_info"
                },
                {
                    "error_path": "",
                    "error_text": "",
                    "id": "32602504-1E4B-4289-BA41-F9A346086582",
                    "meta_type": "none",
                    "res_path": "",
                    "res_text": "",
                    "type": "placeholder_info"
                },
                {
                    "error_path": "",
                    "error_text": "",
                    "id": "E133C72A-A17E-4959-AD31-B6E631DB39C0",
                    "meta_type": "none",
                    "res_path": "",
                    "res_text": "",
                    "type": "placeholder_info"
                }
            ],
            "placeholders": [],
            "plugin_effects": [],
            "primary_color_wheels": [],
            "realtime_denoises": [],
            "shapes": [],
            "smart_crops": [],
            "smart_relights": [],
            "sound_channel_mappings": [
                {
                    "audio_channel_mapping": 0,
                    "id": "14923CD0-726C-459e-A8B0-3F68FCCFBCDA",
                    "is_config_open": false,
                    "type": "none"
                },
                {
                    "audio_channel_mapping": 0,
                    "id": "0421246E-8F93-43e9-BF43-E1858DC1FF5D",
                    "is_config_open": false,
                    "type": "none"
                },
                {
                    "audio_channel_mapping": 0,
                    "id": "83F8B9B6-05FC-451f-BA51-107139FC895B",
                    "is_config_open": false,
                    "type": "none"
                },
                {
                    "audio_channel_mapping": 0,
                    "id": "CF854DB7-CECF-4675-90AC-8B83917F3903",
                    "is_config_open": false,
                    "type": "none"
                }
            ],
            "speeds": [
                {
                    "curve_speed": null,
                    "id": "BC58DAE2-0E22-4284-8635-D41B143173C4",
                    "mode": 0,
                    "speed": 1,
                    "type": "speed"
                },
                {
                    "curve_speed": null,
                    "id": "7D027F2F-2326-4966-A531-51D7EE1D516C",
                    "mode": 0,
                    "speed": 1,
                    "type": "speed"
                },
                {
                    "curve_speed": null,
                    "id": "9346A8F0-54D9-4899-93E0-5DE78E019F55",
                    "mode": 0,
                    "speed": 1,
                    "type": "speed"
                },
                {
                    "curve_speed": null,
                    "id": "A6561F0E-FC3E-4a55-AA0A-44E68900B221",
                    "mode": 0,
                    "speed": 1,
                    "type": "speed"
                }
            ],
            "stickers": [],
            "tail_leaders": [],
            "text_templates": [],
            "texts": [
                {
                    "add_type": 0,
                    "alignment": 1,
                    "background_alpha": 0.65,
                    "background_color": "#000000",
                    "background_fill": "",
                    "background_height": 0.14,
                    "background_horizontal_offset": 0,
                    "background_round_radius": 0.25,
                    "background_style": 1,
                    "background_vertical_offset": 0,
                    "background_width": 0.14,
                    "base_content": "",
                    "bold_width": 0,
                    "border_alpha": 1,
                    "border_color": "#000000",
                    "border_width": 0.058953169733285904,
                    "caption_template_info": {
                        "category_id": "",
                        "category_name": "",
                        "effect_id": "",
                        "is_new": false,
                        "path": "",
                        "request_id": "",
                        "resource_id": "",
                        "resource_name": "",
                        "source_platform": 0,
                        "third_resource_id": ""
                    },
                    "check_flag": 27,
                    "combo_info": {
                        "text_templates": []
                    },
                    "content": "{\"text\":\"I was wide awake when it happened. Not from\\nNot from nerves.\",\"styles\":[{\"fill\":{\"content\":{\"render_type\":\"solid\",\"solid\":{\"color\":[1,1,1]}}},\"font\":{\"path\":\"C:/Users/<USER>/AppData/Local/CapCut/User Data/Cache/effect/7148700152578970113/85f15a13350d034701083f3b2ff95f96/OpenSans-Semibold.ttf\",\"id\":\"7148700152578970113\"},\"strokes\":[{\"content\":{\"render_type\":\"solid\",\"solid\":{\"color\":[0,0,0]}},\"width\":0.0589531697332859}],\"size\":7,\"range\":[0,60]}]}",
                    "current_words": {
                        "end_time": [],
                        "start_time": [],
                        "text": []
                    },
                    "cutoff_postfix": "",
                    "fixed_height": -1,
                    "fixed_width": -1,
                    "font_category_id": "",
                    "font_category_name": "",
                    "font_id": "",
                    "font_name": "",
                    "font_path": "C:/Users/<USER>/AppData/Local/CapCut/User Data/Cache/effect/7148700152578970113/85f15a13350d034701083f3b2ff95f96/OpenSans-Semibold.ttf",
                    "font_resource_id": "7148700152578970113",
                    "font_size": 7,
                    "font_source_platform": 1,
                    "font_team_id": "",
                    "font_third_resource_id": "",
                    "font_title": "none",
                    "font_url": "",
                    "fonts": [
                        {
                            "category_id": "favoured",
                            "category_name": "Favorites",
                            "effect_id": "7148700152578970113",
                            "file_uri": "",
                            "id": "38D913E5-7262-41f4-B89E-01E29F48A3BF",
                            "path": "C:/Users/<USER>/AppData/Local/CapCut/User Data/Cache/effect/7148700152578970113/85f15a13350d034701083f3b2ff95f96/OpenSans-Semibold.ttf",
                            "request_id": "202505200905470101920340071175433",
                            "resource_id": "7148700152578970113",
                            "source_platform": 1,
                            "team_id": "",
                            "third_resource_id": "",
                            "title": "OpenSans2"
                        }
                    ],
                    "force_apply_line_max_width": false,
                    "global_alpha": 1,
                    "group_id": "",
                    "has_shadow": false,
                    "id": "A93716DF-09BB-426c-90C0-C225B1DD4A03",
                    "initial_scale": 1,
                    "inner_padding": -1,
                    "is_lyric_effect": false,
                    "is_rich_text": false,
                    "is_words_linear": false,
                    "italic_degree": 0,
                    "ktv_color": "",
                    "language": "",
                    "layer_weight": 1,
                    "letter_spacing": 0,
                    "line_feed": 1,
                    "line_max_width": 0.82,
                    "line_spacing": 0.32,
                    "lyric_group_id": "",
                    "lyrics_template": {
                        "category_id": "",
                        "category_name": "",
                        "effect_id": "",
                        "panel": "",
                        "path": "",
                        "request_id": "",
                        "resource_id": "",
                        "resource_name": ""
                    },
                    "multi_language_current": "none",
                    "name": "",
                    "oneline_cutoff": false,
                    "operation_type": 0,
                    "original_size": [],
                    "preset_category": "",
                    "preset_category_id": "",
                    "preset_has_set_alignment": false,
                    "preset_id": "",
                    "preset_index": 0,
                    "preset_name": "",
                    "recognize_task_id": "",
                    "recognize_text": "",
                    "recognize_type": 0,
                    "relevance_segment": [],
                    "shadow_alpha": 0.9,
                    "shadow_angle": -45,
                    "shadow_color": "#000000",
                    "shadow_distance": 5,
                    "shadow_point": {
                        "x": 0.6363961030678928,
                        "y": -0.6363961030678928
                    },
                    "shadow_smoothing": 0.45,
                    "shape_clip_x": false,
                    "shape_clip_y": false,
                    "source_from": "",
                    "ssml_content": "",
                    "style_name": "",
                    "sub_template_id": -1,
                    "sub_type": 0,
                    "subtitle_keywords": null,
                    "subtitle_keywords_config": null,
                    "subtitle_template_original_fontsize": 0,
                    "text_alpha": 1,
                    "text_color": "#FFFFFF",
                    "text_curve": null,
                    "text_preset_resource_id": "",
                    "text_size": 30,
                    "text_to_audio_ids": [],
                    "tts_auto_update": false,
                    "type": "text",
                    "typesetting": 0,
                    "underline": false,
                    "underline_offset": 0.22,
                    "underline_width": 0.05,
                    "use_effect_default_color": true,
                    "words": {
                        "end_time": [],
                        "start_time": [],
                        "text": []
                    }
                }
            ],
            "time_marks": [],
            "transitions": [
                {
                    "category_id": "25835",
                    "category_name": "Trending",
                    "duration": 466666,
                    "effect_id": "6724845717472416269",
                    "id": "3457D6A5-9CC4-42f3-91F0-5A39399395E1",
                    "is_overlap": true,
                    "name": "Mix",
                    "path": "C:/Users/<USER>/AppData/Local/CapCut/User Data/Cache/effect/6724845717472416269/7b53f4c008c4c684fccf8c7d4d46cc92",
                    "platform": "all",
                    "request_id": "202505031515537C52C2C63193255A3A70",
                    "resource_id": "6724845717472416269",
                    "source_platform": 1,
                    "third_resource_id": "6724845717472416269",
                    "type": "transition"
                }
            ],
            "video_effects": [
                {
                    "adjust_params": [
                        {
                            "default_value": 0.2,
                            "name": "effects_adjust_speed",
                            "value": 0.2
                        },
                        {
                            "default_value": 1,
                            "name": "sticker",
                            "value": 1
                        },
                        {
                            "default_value": 0.4,
                            "name": "effects_adjust_filter",
                            "value": 0.4
                        }
                    ],
                    "algorithm_artifact_path": "",
                    "apply_target_type": 2,
                    "apply_time_range": null,
                    "bind_segment_id": "",
                    "category_id": "100000",
                    "category_name": "Video effects",
                    "common_keyframes": [],
                    "covering_relation_change": 0,
                    "disable_effect_faces": [],
                    "effect_id": "7399466759058918662",
                    "effect_mask": [],
                    "enable_mask": true,
                    "formula_id": "",
                    "id": "C7C3D39F-480E-4f42-86A3-FFD34E2E7F63",
                    "item_effect_type": 0,
                    "name": "By the Fireplace",
                    "path": "C:/Users/<USER>/AppData/Local/CapCut/User Data/Cache/effect/7399466759058918662/6422961dbe02f34709668b2f7cac5ea7",
                    "platform": "all",
                    "render_index": 0,
                    "request_id": "",
                    "resource_id": "7399466759058918662",
                    "source_platform": 1,
                    "sub_type": 0,
                    "time_range": null,
                    "track_render_index": 0,
                    "transparent_params": "",
                    "type": "video_effect",
                    "value": 1,
                    "version": ""
                }
            ],
            "video_trackings": [],
            "videos": [
                {
                    "aigc_history_id": "",
                    "aigc_item_id": "",
                    "aigc_type": "none",
                    "audio_fade": null,
                    "beauty_body_preset_id": "",
                    "beauty_face_auto_preset": {
                        "name": "",
                        "preset_id": "",
                        "rate_map": ""
                    },
                    "beauty_face_auto_preset_infos": [],
                    "beauty_face_preset_infos": [],
                    "cartoon_path": "",
                    "category_id": "",
                    "category_name": "local",
                    "check_flag": 62978047,
                    "crop": {
                        "lower_left_x": 0,
                        "lower_left_y": 1,
                        "lower_right_x": 1,
                        "lower_right_y": 1,
                        "upper_left_x": 0,
                        "upper_left_y": 0,
                        "upper_right_x": 1,
                        "upper_right_y": 0
                    },
                    "crop_ratio": "free",
                    "crop_scale": 1,
                    "duration": 10800000000,
                    "extra_type_option": 0,
                    "formula_id": "",
                    "freeze": null,
                    "has_audio": false,
                    "has_sound_separated": false,
                    "height": 664,
                    "id": "B1B340F8-7BAA-4338-B817-AD6A41B68281",
                    "intensifies_audio_path": "",
                    "intensifies_path": "",
                    "is_ai_generate_content": false,
                    "is_copyright": false,
                    "is_text_edit_overdub": false,
                    "is_unified_beauty_mode": false,
                    "live_photo_cover_path": "",
                    "live_photo_timestamp": -1,
                    "local_id": "",
                    "local_material_from": "",
                    "local_material_id": "",
                    "material_id": "",
                    "material_name": "1 - 1.jpg",
                    "material_url": "",
                    "matting": {
                        "custom_matting_id": "",
                        "enable_matting_stroke": false,
                        "expansion": 0,
                        "feather": 0,
                        "flag": 0,
                        "has_use_quick_brush": false,
                        "has_use_quick_eraser": false,
                        "interactiveTime": [],
                        "path": "",
                        "reverse": false,
                        "strokes": []
                    },
                    "media_path": "",
                    "multi_camera_info": null,
                    "object_locked": null,
                    "origin_material_id": "",
                    "path": "C:/Users/<USER>/OneDrive/YoutubeContent/The Last Straw Stories/At Our Anniversary Feast, My MIL Declared My Replacement—She Was Mistaken/Pictures/1 - 1.jpg",
                    "picture_from": "none",
                    "picture_set_category_id": "",
                    "picture_set_category_name": "",
                    "request_id": "",
                    "reverse_intensifies_path": "",
                    "reverse_path": "",
                    "smart_match_info": null,
                    "smart_motion": null,
                    "source": 0,
                    "source_platform": 0,
                    "stable": {
                        "matrix_path": "",
                        "stable_level": 0,
                        "time_range": {
                            "duration": 0,
                            "start": 0
                        }
                    },
                    "team_id": "",
                    "type": "photo",
                    "video_algorithm": {
                        "ai_background_configs": [],
                        "ai_expression_driven": null,
                        "ai_motion_driven": null,
                        "aigc_generate": null,
                        "algorithms": [],
                        "complement_frame_config": null,
                        "deflicker": null,
                        "gameplay_configs": [],
                        "motion_blur_config": null,
                        "mouth_shape_driver": null,
                        "noise_reduction": null,
                        "path": "",
                        "quality_enhance": null,
                        "smart_complement_frame": null,
                        "super_resolution": null,
                        "time_range": null
                    },
                    "width": 1184
                },
                {
                    "aigc_history_id": "",
                    "aigc_item_id": "",
                    "aigc_type": "none",
                    "audio_fade": null,
                    "beauty_body_preset_id": "",
                    "beauty_face_auto_preset": {
                        "name": "",
                        "preset_id": "",
                        "rate_map": ""
                    },
                    "beauty_face_auto_preset_infos": [],
                    "beauty_face_preset_infos": [],
                    "cartoon_path": "",
                    "category_id": "",
                    "category_name": "",
                    "check_flag": 62978047,
                    "crop": {
                        "lower_left_x": 0,
                        "lower_left_y": 1,
                        "lower_right_x": 1,
                        "lower_right_y": 1,
                        "upper_left_x": 0,
                        "upper_left_y": 0,
                        "upper_right_x": 1,
                        "upper_right_y": 0
                    },
                    "crop_ratio": "free",
                    "crop_scale": 1,
                    "duration": 10800000000,
                    "extra_type_option": 0,
                    "formula_id": "",
                    "freeze": null,
                    "has_audio": false,
                    "has_sound_separated": false,
                    "height": 664,
                    "id": "F8876EED-35A8-4f8e-877C-83AAFDA508D6",
                    "intensifies_audio_path": "",
                    "intensifies_path": "",
                    "is_ai_generate_content": false,
                    "is_copyright": false,
                    "is_text_edit_overdub": false,
                    "is_unified_beauty_mode": false,
                    "live_photo_cover_path": "",
                    "live_photo_timestamp": -1,
                    "local_id": "",
                    "local_material_from": "",
                    "local_material_id": "",
                    "material_id": "",
                    "material_name": "1 - 1.jpg",
                    "material_url": "",
                    "matting": {
                        "custom_matting_id": "",
                        "enable_matting_stroke": false,
                        "expansion": 0,
                        "feather": 0,
                        "flag": 0,
                        "has_use_quick_brush": false,
                        "has_use_quick_eraser": false,
                        "interactiveTime": [],
                        "path": "",
                        "reverse": false,
                        "strokes": []
                    },
                    "media_path": "",
                    "multi_camera_info": null,
                    "object_locked": null,
                    "origin_material_id": "",
                    "path": "C:/Users/<USER>/OneDrive/YoutubeContent/The Last Straw Stories/My Mother-in-Law Rewrote My Wedding Promises in Secret—My Words at the Altar Made Her Rethink Everything/Pictures/1 - 1.jpg",
                    "picture_from": "none",
                    "picture_set_category_id": "",
                    "picture_set_category_name": "",
                    "request_id": "",
                    "reverse_intensifies_path": "",
                    "reverse_path": "",
                    "smart_match_info": null,
                    "smart_motion": null,
                    "source": 5,
                    "source_platform": 0,
                    "stable": {
                        "matrix_path": "",
                        "stable_level": 0,
                        "time_range": {
                            "duration": 0,
                            "start": 0
                        }
                    },
                    "team_id": "",
                    "type": "photo",
                    "video_algorithm": {
                        "ai_background_configs": [],
                        "ai_expression_driven": null,
                        "ai_motion_driven": null,
                        "aigc_generate": null,
                        "algorithms": [],
                        "complement_frame_config": null,
                        "deflicker": null,
                        "gameplay_configs": [],
                        "motion_blur_config": null,
                        "mouth_shape_driver": null,
                        "noise_reduction": null,
                        "path": "",
                        "quality_enhance": null,
                        "smart_complement_frame": null,
                        "super_resolution": null,
                        "time_range": null
                    },
                    "width": 1184
                },
                {
                    "aigc_history_id": "",
                    "aigc_item_id": "",
                    "aigc_type": "none",
                    "audio_fade": {
                        "fade_in_duration": 0,
                        "fade_out_duration": 0,
                        "fade_type": 0,
                        "id": "4906D5EB-4041-42fa-AE0A-19F0FAD7BAC0",
                        "type": "audio_fade"
                    },
                    "beauty_body_preset_id": "",
                    "beauty_face_auto_preset": {
                        "name": "",
                        "preset_id": "",
                        "rate_map": ""
                    },
                    "beauty_face_auto_preset_infos": [],
                    "beauty_face_preset_infos": [],
                    "cartoon_path": "",
                    "category_id": "",
                    "category_name": "local",
                    "check_flag": 62978047,
                    "crop": {
                        "lower_left_x": 0,
                        "lower_left_y": 1,
                        "lower_right_x": 1,
                        "lower_right_y": 1,
                        "upper_left_x": 0,
                        "upper_left_y": 0,
                        "upper_right_x": 1,
                        "upper_right_y": 0
                    },
                    "crop_ratio": "free",
                    "crop_scale": 1,
                    "duration": 22433333,
                    "extra_type_option": 0,
                    "formula_id": "",
                    "freeze": null,
                    "has_audio": true,
                    "has_sound_separated": false,
                    "height": 2160,
                    "id": "255B87C3-5BED-4d54-95B0-208A7EE99293",
                    "intensifies_audio_path": "",
                    "intensifies_path": "",
                    "is_ai_generate_content": false,
                    "is_copyright": false,
                    "is_text_edit_overdub": false,
                    "is_unified_beauty_mode": false,
                    "live_photo_cover_path": "",
                    "live_photo_timestamp": -1,
                    "local_id": "",
                    "local_material_from": "",
                    "local_material_id": "40373f93-3a7e-4fcd-8c18-14682778719c",
                    "material_id": "",
                    "material_name": "3094026-uhd_3840_2160_30fps.mp4",
                    "material_url": "",
                    "matting": {
                        "custom_matting_id": "",
                        "enable_matting_stroke": false,
                        "expansion": 0,
                        "feather": 0,
                        "flag": 0,
                        "has_use_quick_brush": false,
                        "has_use_quick_eraser": false,
                        "interactiveTime": [],
                        "path": "",
                        "reverse": false,
                        "strokes": []
                    },
                    "media_path": "",
                    "multi_camera_info": null,
                    "object_locked": null,
                    "origin_material_id": "",
                    "path": "C:/Users/<USER>/Downloads/3094026-uhd_3840_2160_30fps.mp4",
                    "picture_from": "none",
                    "picture_set_category_id": "",
                    "picture_set_category_name": "",
                    "request_id": "",
                    "reverse_intensifies_path": "",
                    "reverse_path": "",
                    "smart_match_info": null,
                    "smart_motion": null,
                    "source": 0,
                    "source_platform": 0,
                    "stable": {
                        "matrix_path": "",
                        "stable_level": 0,
                        "time_range": {
                            "duration": 0,
                            "start": 0
                        }
                    },
                    "team_id": "",
                    "type": "video",
                    "video_algorithm": {
                        "ai_background_configs": [],
                        "ai_expression_driven": null,
                        "ai_motion_driven": null,
                        "aigc_generate": null,
                        "algorithms": [],
                        "complement_frame_config": null,
                        "deflicker": null,
                        "gameplay_configs": [],
                        "motion_blur_config": null,
                        "mouth_shape_driver": null,
                        "noise_reduction": null,
                        "path": "",
                        "quality_enhance": null,
                        "smart_complement_frame": null,
                        "super_resolution": null,
                        "time_range": {
                            "duration": 12033333,
                            "start": 0
                        }
                    },
                    "width": 3840
                }
            ],
            "vocal_beautifys": [],
            "vocal_separations": [
                {
                    "choice": 0,
                    "id": "1DDDEF2D-AADC-4993-9AD9-779092C45F7C",
                    "production_path": "",
                    "removed_sounds": [],
                    "time_range": null,
                    "type": "vocal_separation"
                },
                {
                    "choice": 0,
                    "id": "00B63C16-4CE4-4915-BED4-821E6F1B73A0",
                    "production_path": "",
                    "removed_sounds": [],
                    "time_range": null,
                    "type": "vocal_separation"
                },
                {
                    "choice": 0,
                    "id": "79180445-0144-4806-8ACD-1F010B6313D5",
                    "production_path": "",
                    "removed_sounds": [],
                    "time_range": null,
                    "type": "vocal_separation"
                },
                {
                    "choice": 0,
                    "id": "BE387B94-88FC-45aa-BF6A-A462640B4221",
                    "production_path": "",
                    "removed_sounds": [],
                    "time_range": null,
                    "type": "vocal_separation"
                }
            ]
        },
        "mutable_config": null,
        "name": "",
        "new_version": "134.0.0",
        "path": "",
        "platform": {
            "app_id": 359289,
            "app_source": "cc",
            "app_version": "6.1.2",
            "device_id": "7c398101c365c816a7452e58d2e0f482",
            "hard_disk_id": "",
            "mac_address": "7eae0635fa09643462eaf40cd9597a2d,d9aed9fab7102f4f36bd91f70f9b043e,10d2717e7d06a80d409fbb21516ebec0",
            "os": "windows",
            "os_version": "10.0.26100"
        },
        "relationships": [],
        "render_index_track_mode_on": true,
        "retouch_cover": null,
        "source": "default",
        "static_cover_image_path": "",
        "time_marks": null,
        "tracks": [
            {
                "attribute": 0,
                "flag": 0,
                "id": "4DE3C187-C463-4520-BA8E-4213F31DBB01",
                "is_default_name": true,
                "name": "",
                "segments": [
                    {
                        "caption_info": null,
                        "cartoon": false,
                        "clip": {
                            "alpha": 1,
                            "flip": {
                                "horizontal": false,
                                "vertical": false
                            },
                            "rotation": 0,
                            "scale": {
                                "x": 0.9999999999999998,
                                "y": 0.9999999999999998
                            },
                            "transform": {
                                "x": 0,
                                "y": 0
                            }
                        },
                        "color_correct_alg_result": "",
                        "common_keyframes": [
                            {
                                "id": "94CA7580-EF63-41db-BB52-A551A71476AD",
                                "keyframe_list": [
                                    {
                                        "curveType": "Line",
                                        "graphID": "",
                                        "id": "8D6A6E6B-D391-4446-AC37-BB6B91BBAF06",
                                        "left_control": {
                                            "x": 0,
                                            "y": 0
                                        },
                                        "right_control": {
                                            "x": 0,
                                            "y": 0
                                        },
                                        "string_value": "",
                                        "time_offset": 600000,
                                        "values": [
                                            1.0199999999999998
                                        ]
                                    },
                                    {
                                        "curveType": "Line",
                                        "graphID": "",
                                        "id": "13483A1F-EFC1-4210-B1ED-414588793E84",
                                        "left_control": {
                                            "x": 0,
                                            "y": 0
                                        },
                                        "right_control": {
                                            "x": 0,
                                            "y": 0
                                        },
                                        "string_value": "",
                                        "time_offset": 3600000,
                                        "values": [
                                            0.9999999999999998
                                        ]
                                    }
                                ],
                                "material_id": "",
                                "property_type": "KFTypeScaleX"
                            }
                        ],
                        "desc": "",
                        "digital_human_template_group_id": "",
                        "enable_adjust": true,
                        "enable_adjust_mask": false,
                        "enable_color_correct_adjust": false,
                        "enable_color_curves": true,
                        "enable_color_match_adjust": false,
                        "enable_color_wheels": true,
                        "enable_hsl": true,
                        "enable_lut": true,
                        "enable_smart_color_adjust": false,
                        "enable_video_mask": true,
                        "extra_material_refs": [
                            "BC58DAE2-0E22-4284-8635-D41B143173C4",
                            "D300E548-B699-4987-81DB-104C609D00E9",
                            "FB4AEE52-6132-496e-B50D-4B227E1231EE",
                            "3457D6A5-9CC4-42f3-91F0-5A39399395E1",
                            "3BF94175-986B-4ffc-8EBA-2C09B41D478D",
                            "785F460F-A80D-4092-8E8C-0CCEDB4495A3",
                            "14923CD0-726C-459e-A8B0-3F68FCCFBCDA",
                            "8CAA68CC-BD5B-430a-AF4F-60A25457DB8B",
                            "1DDDEF2D-AADC-4993-9AD9-779092C45F7C"
                        ],
                        "group_id": "",
                        "hdr_settings": {
                            "intensity": 1,
                            "mode": 1,
                            "nits": 1000
                        },
                        "id": "5CAFC811-8E1B-4068-8112-FAA495A214E9",
                        "intensifies_audio": false,
                        "is_loop": false,
                        "is_placeholder": false,
                        "is_tone_modify": false,
                        "keyframe_refs": [],
                        "last_nonzero_volume": 1,
                        "lyric_keyframes": null,
                        "material_id": "B1B340F8-7BAA-4338-B817-AD6A41B68281",
                        "raw_segment_id": "",
                        "render_index": 0,
                        "render_timerange": {
                            "duration": 0,
                            "start": 0
                        },
                        "responsive_layout": {
                            "enable": false,
                            "horizontal_pos_layout": 0,
                            "size_layout": 0,
                            "target_follow": "",
                            "vertical_pos_layout": 0
                        },
                        "reverse": false,
                        "source_timerange": {
                            "duration": 35000000,
                            "start": 0
                        },
                        "speed": 1,
                        "state": 0,
                        "target_timerange": {
                            "duration": 35000000,
                            "start": 0
                        },
                        "template_id": "",
                        "template_scene": "default",
                        "track_attribute": 0,
                        "track_render_index": 0,
                        "uniform_scale": {
                            "on": true,
                            "value": 1
                        },
                        "visible": true,
                        "volume": 1
                    },
                    {
                        "caption_info": null,
                        "cartoon": false,
                        "clip": {
                            "alpha": 1,
                            "flip": {
                                "horizontal": false,
                                "vertical": false
                            },
                            "rotation": 0,
                            "scale": {
                                "x": 1.0500000000000005,
                                "y": 1.0500000000000005
                            },
                            "transform": {
                                "x": 0,
                                "y": 0
                            }
                        },
                        "color_correct_alg_result": "",
                        "common_keyframes": [],
                        "desc": "",
                        "digital_human_template_group_id": "",
                        "enable_adjust": true,
                        "enable_adjust_mask": false,
                        "enable_color_correct_adjust": false,
                        "enable_color_curves": true,
                        "enable_color_match_adjust": false,
                        "enable_color_wheels": true,
                        "enable_hsl": false,
                        "enable_lut": true,
                        "enable_smart_color_adjust": false,
                        "enable_video_mask": true,
                        "extra_material_refs": [
                            "7D027F2F-2326-4966-A531-51D7EE1D516C",
                            "5F32E864-AC12-4155-8020-BEEDB0DD96CA",
                            "9EDA9AD9-1501-4503-BE4E-E4E4ED242DA5",
                            "BCED53E8-D5BA-43bf-AFAE-AABE43BEE010",
                            "0421246E-8F93-43e9-BF43-E1858DC1FF5D",
                            "AA80BF02-C707-4570-BDCC-1F2C4F37BA16",
                            "00B63C16-4CE4-4915-BED4-821E6F1B73A0"
                        ],
                        "group_id": "",
                        "hdr_settings": {
                            "intensity": 1,
                            "mode": 1,
                            "nits": 1000
                        },
                        "id": "3854FC27-3688-4b6c-BC34-426DED021452",
                        "intensifies_audio": false,
                        "is_loop": false,
                        "is_placeholder": false,
                        "is_tone_modify": false,
                        "keyframe_refs": [],
                        "last_nonzero_volume": 1,
                        "lyric_keyframes": null,
                        "material_id": "F8876EED-35A8-4f8e-877C-83AAFDA508D6",
                        "raw_segment_id": "",
                        "render_index": 0,
                        "render_timerange": {
                            "duration": 0,
                            "start": 0
                        },
                        "responsive_layout": {
                            "enable": false,
                            "horizontal_pos_layout": 0,
                            "size_layout": 0,
                            "target_follow": "",
                            "vertical_pos_layout": 0
                        },
                        "reverse": false,
                        "source_timerange": {
                            "duration": 35000000,
                            "start": 0
                        },
                        "speed": 1,
                        "state": 0,
                        "target_timerange": {
                            "duration": 35000000,
                            "start": 35000000
                        },
                        "template_id": "",
                        "template_scene": "default",
                        "track_attribute": 0,
                        "track_render_index": 0,
                        "uniform_scale": {
                            "on": true,
                            "value": 1
                        },
                        "visible": true,
                        "volume": 1
                    },
                    {
                        "caption_info": null,
                        "cartoon": false,
                        "clip": {
                            "alpha": 1,
                            "flip": {
                                "horizontal": false,
                                "vertical": false
                            },
                            "rotation": 0,
                            "scale": {
                                "x": 1,
                                "y": 1
                            },
                            "transform": {
                                "x": 0,
                                "y": 0
                            }
                        },
                        "color_correct_alg_result": "",
                        "common_keyframes": [],
                        "desc": "",
                        "digital_human_template_group_id": "",
                        "enable_adjust": true,
                        "enable_adjust_mask": false,
                        "enable_color_correct_adjust": false,
                        "enable_color_curves": true,
                        "enable_color_match_adjust": false,
                        "enable_color_wheels": true,
                        "enable_hsl": false,
                        "enable_lut": true,
                        "enable_smart_color_adjust": false,
                        "enable_video_mask": true,
                        "extra_material_refs": [
                            "9346A8F0-54D9-4899-93E0-5DE78E019F55",
                            "32602504-1E4B-4289-BA41-F9A346086582",
                            "4906D5EB-4041-42fa-AE0A-19F0FAD7BAC0",
                            "E50927BC-B349-4216-A773-695C760D9B21",
                            "061DD99C-12DF-4bf0-A139-3F10923DAE8A",
                            "83F8B9B6-05FC-451f-BA51-107139FC895B",
                            "79180445-0144-4806-8ACD-1F010B6313D5"
                        ],
                        "group_id": "",
                        "hdr_settings": {
                            "intensity": 1,
                            "mode": 1,
                            "nits": 1000
                        },
                        "id": "F1C6EA9B-CE17-444e-A96A-796AF2437C0B",
                        "intensifies_audio": false,
                        "is_loop": false,
                        "is_placeholder": false,
                        "is_tone_modify": false,
                        "keyframe_refs": [],
                        "last_nonzero_volume": 1,
                        "lyric_keyframes": null,
                        "material_id": "255B87C3-5BED-4d54-95B0-208A7EE99293",
                        "raw_segment_id": "",
                        "render_index": 0,
                        "render_timerange": {
                            "duration": 0,
                            "start": 0
                        },
                        "responsive_layout": {
                            "enable": false,
                            "horizontal_pos_layout": 0,
                            "size_layout": 0,
                            "target_follow": "",
                            "vertical_pos_layout": 0
                        },
                        "reverse": false,
                        "source_timerange": {
                            "duration": 9033333,
                            "start": 0
                        },
                        "speed": 1,
                        "state": 0,
                        "target_timerange": {
                            "duration": 9033333,
                            "start": 70000000
                        },
                        "template_id": "",
                        "template_scene": "default",
                        "track_attribute": 0,
                        "track_render_index": 0,
                        "uniform_scale": {
                            "on": true,
                            "value": 1
                        },
                        "visible": true,
                        "volume": 1
                    }
                ],
                "type": "video"
            },
            {
                "attribute": 0,
                "flag": 0,
                "id": "5694EFEE-755B-467d-A297-CAB601B44216",
                "is_default_name": true,
                "name": "",
                "segments": [
                    {
                        "caption_info": null,
                        "cartoon": false,
                        "clip": null,
                        "color_correct_alg_result": "",
                        "common_keyframes": [],
                        "desc": "",
                        "digital_human_template_group_id": "",
                        "enable_adjust": false,
                        "enable_adjust_mask": false,
                        "enable_color_correct_adjust": false,
                        "enable_color_curves": true,
                        "enable_color_match_adjust": false,
                        "enable_color_wheels": true,
                        "enable_hsl": false,
                        "enable_lut": false,
                        "enable_smart_color_adjust": false,
                        "enable_video_mask": true,
                        "extra_material_refs": [],
                        "group_id": "",
                        "hdr_settings": null,
                        "id": "90739276-854D-478f-BF41-FB77BD8A6407",
                        "intensifies_audio": false,
                        "is_loop": false,
                        "is_placeholder": false,
                        "is_tone_modify": false,
                        "keyframe_refs": [],
                        "last_nonzero_volume": 1,
                        "lyric_keyframes": null,
                        "material_id": "C7C3D39F-480E-4f42-86A3-FFD34E2E7F63",
                        "raw_segment_id": "",
                        "render_index": 11000,
                        "render_timerange": {
                            "duration": 0,
                            "start": 0
                        },
                        "responsive_layout": {
                            "enable": false,
                            "horizontal_pos_layout": 0,
                            "size_layout": 0,
                            "target_follow": "",
                            "vertical_pos_layout": 0
                        },
                        "reverse": false,
                        "source_timerange": null,
                        "speed": 1,
                        "state": 0,
                        "target_timerange": {
                            "duration": 6200000,
                            "start": 0
                        },
                        "template_id": "",
                        "template_scene": "default",
                        "track_attribute": 0,
                        "track_render_index": 1,
                        "uniform_scale": null,
                        "visible": true,
                        "volume": 1
                    }
                ],
                "type": "effect"
            },
            {
                "attribute": 0,
                "flag": 0,
                "id": "3D9D05AF-CE30-49aa-8D98-7DF482309BAC",
                "is_default_name": true,
                "name": "",
                "segments": [
                    {
                        "caption_info": null,
                        "cartoon": false,
                        "clip": {
                            "alpha": 1,
                            "flip": {
                                "horizontal": false,
                                "vertical": false
                            },
                            "rotation": 0,
                            "scale": {
                                "x": 1,
                                "y": 1
                            },
                            "transform": {
                                "x": 0,
                                "y": -0.6211904883384705
                            }
                        },
                        "color_correct_alg_result": "",
                        "common_keyframes": [],
                        "desc": "",
                        "digital_human_template_group_id": "",
                        "enable_adjust": false,
                        "enable_adjust_mask": false,
                        "enable_color_correct_adjust": false,
                        "enable_color_curves": true,
                        "enable_color_match_adjust": false,
                        "enable_color_wheels": true,
                        "enable_hsl": false,
                        "enable_lut": false,
                        "enable_smart_color_adjust": false,
                        "enable_video_mask": true,
                        "extra_material_refs": [
                            "265D24D4-589F-4ec4-8E7E-4C59AC916501"
                        ],
                        "group_id": "",
                        "hdr_settings": null,
                        "id": "8831BCC9-6336-4061-9FAD-5C2E6D63EA2C",
                        "intensifies_audio": false,
                        "is_loop": false,
                        "is_placeholder": false,
                        "is_tone_modify": false,
                        "keyframe_refs": [],
                        "last_nonzero_volume": 1,
                        "lyric_keyframes": null,
                        "material_id": "A93716DF-09BB-426c-90C0-C225B1DD4A03",
                        "raw_segment_id": "",
                        "render_index": 14000,
                        "render_timerange": {
                            "duration": 0,
                            "start": 0
                        },
                        "responsive_layout": {
                            "enable": false,
                            "horizontal_pos_layout": 0,
                            "size_layout": 0,
                            "target_follow": "",
                            "vertical_pos_layout": 0
                        },
                        "reverse": false,
                        "source_timerange": null,
                        "speed": 1,
                        "state": 0,
                        "target_timerange": {
                            "duration": 3000000,
                            "start": 0
                        },
                        "template_id": "",
                        "template_scene": "default",
                        "track_attribute": 0,
                        "track_render_index": 2,
                        "uniform_scale": {
                            "on": true,
                            "value": 1
                        },
                        "visible": true,
                        "volume": 1
                    }
                ],
                "type": "text"
            },
            {
                "attribute": 0,
                "flag": 0,
                "id": "B83FA5E8-F0D6-4c58-8827-628722BA03E2",
                "is_default_name": true,
                "name": "",
                "segments": [
                    {
                        "caption_info": null,
                        "cartoon": false,
                        "clip": null,
                        "color_correct_alg_result": "",
                        "common_keyframes": [],
                        "desc": "",
                        "digital_human_template_group_id": "",
                        "enable_adjust": false,
                        "enable_adjust_mask": false,
                        "enable_color_correct_adjust": false,
                        "enable_color_curves": true,
                        "enable_color_match_adjust": false,
                        "enable_color_wheels": true,
                        "enable_hsl": false,
                        "enable_lut": false,
                        "enable_smart_color_adjust": false,
                        "enable_video_mask": true,
                        "extra_material_refs": [
                            "A6561F0E-FC3E-4a55-AA0A-44E68900B221",
                            "E133C72A-A17E-4959-AD31-B6E631DB39C0",
                            "FFB24248-961B-4281-9B4C-FE5707CF93D9",
                            "CF854DB7-CECF-4675-90AC-8B83917F3903",
                            "BE387B94-88FC-45aa-BF6A-A462640B4221"
                        ],
                        "group_id": "",
                        "hdr_settings": null,
                        "id": "513DA4C2-31DC-4e1c-8D3F-DC0E8C2E2CAD",
                        "intensifies_audio": false,
                        "is_loop": false,
                        "is_placeholder": false,
                        "is_tone_modify": false,
                        "keyframe_refs": [],
                        "last_nonzero_volume": 1,
                        "lyric_keyframes": null,
                        "material_id": "CFCBEF05-3987-40b1-94B3-7BA43CF9C361",
                        "raw_segment_id": "",
                        "render_index": 0,
                        "render_timerange": {
                            "duration": 0,
                            "start": 0
                        },
                        "responsive_layout": {
                            "enable": false,
                            "horizontal_pos_layout": 0,
                            "size_layout": 0,
                            "target_follow": "",
                            "vertical_pos_layout": 0
                        },
                        "reverse": false,
                        "source_timerange": {
                            "duration": 916833333,
                            "start": 0
                        },
                        "speed": 1,
                        "state": 0,
                        "target_timerange": {
                            "duration": 916833333,
                            "start": 0
                        },
                        "template_id": "",
                        "template_scene": "default",
                        "track_attribute": 0,
                        "track_render_index": 3,
                        "uniform_scale": null,
                        "visible": true,
                        "volume": 1
                    }
                ],
                "type": "audio"
            }
        ],
        "uneven_animation_template_info": {
            "composition": "",
            "content": "",
            "order": "",
            "sub_template_info_list": []
        },
        "update_time": 0,
        "version": 360000
    };
    project = (templateFromFile && JSON.parse(JSON.stringify(templateFromFile))) || project;
    return project;
};
exports.templateProject = templateProject;
//# sourceMappingURL=templateProject.js.map