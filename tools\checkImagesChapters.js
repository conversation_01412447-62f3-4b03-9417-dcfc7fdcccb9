import pfs from 'fs/promises'
import path from 'path'
import fs from 'fs'

const readImagesAndChapters = async (assetDir) => {
    // find image dir
    const files = await pfs.readdir(assetDir)
    const hasImage = files.filter(f => {
        const p = path.join(assetDir, f)
        if (!fs.statSync(p).isDirectory()) {
            return false
        }
        const files = fs.readdirSync(p)
        return files.some(f => f.endsWith('.jpg') || f.endsWith('.png'))
    })
    const imageDir = hasImage[0]
    if (!imageDir) {
        throw new Error("No image directory found in assets directory " + assetDir)
    }
    // get images full path
    const imageDirPath = path.join(assetDir, imageDir)
    const images = fs.readdirSync(imageDirPath)
        .filter(f => f.endsWith('.jpg') || f.endsWith('.png') || f.endsWith('.jpeg'))
        .map(f => path.join(imageDirPath, f))

    // get chapters
    const chapterPath = fs.readdirSync(assetDir)
        .filter(f => f.startsWith('chapter') && f.endsWith('.txt'))
        .map(f => path.join(assetDir, f))[0]
    if (!chapterPath) {
        throw new Error("No chapter file found in assets directory " + assetDir)
    }
    const lines = (await pfs.readFile(chapterPath, 'utf8'))
        .split('\n')
        .map(l => l.trim().toLowerCase())
        .filter(l => l)
    /** @type {Array<string>} */
    const chapters = []
    for (let i = 0; i < lines.length; i++) {
        if (lines[i].includes('----')) {
            ++i
            const firstSentence = lines[i].substring(0, 50).toLowerCase()
            chapters.push(firstSentence.replace(/\W/g, ''))
        }
    }
    if (chapters.length !== images.length) {
        throw new Error(`Number of chapters (${chapters.length}) and images (${images.length}) are not equal`)
    }
    return {images, chapters}
}

async function main() {
    const dirs = await pfs.readdir('.')
    for (let dir of dirs) {
        const project = path.join('.', dir)
        try {
            if (!fs.statSync(project).isDirectory()) {
                continue
            }
            await readImagesAndChapters(project)
            console.log(Utils.chalk.paint('Images and chapters OK', 'green'), path.basename(project))
        } catch (e) {
            console.error(Utils.chalk.paint(e.message, 'red'), path.basename(project))
        }
    }
}

export const Utils = {
    chalk: {
        foregroundColors: {
            'black': '\x1b[30m',
            'red': '\x1b[31m',
            'green': '\x1b[32m',
            'yellow': '\x1b[33m',
            'blue': '\x1b[34m',
            'magenta': '\x1b[35m',
            'cyan': '\x1b[36m',
            'white': '\x1b[37m',
            'bright-black': '\x1b[90m',
            'bright-red': '\x1b[91m',
            'bright-green': '\x1b[92m',
            'bright-yellow': '\x1b[93m',
            'bright-blue': '\x1b[94m',
            'bright-magenta': '\x1b[95m',
            'bright-cyan': '\x1b[96m',
            'bright-white': '\x1b[97m',
            'none': '\x1b[0m'
        },
        backgroundColors: {
            'black': '\x1b[40m',
            'red': '\x1b[41m',
            'green': '\x1b[42m',
            'yellow': '\x1b[43m',
            'blue': '\x1b[44m',
            'magenta': '\x1b[45m',
            'cyan': '\x1b[46m',
            'white': '\x1b[47m',
            'bright-black': '\x1b[100m',
            'bright-red': '\x1b[101m',
            'bright-green': '\x1b[102m',
            'bright-yellow': '\x1b[103m',
            'bright-blue': '\x1b[104m',
            'bright-magenta': '\x1b[105m',
            'bright-cyan': '\x1b[106m',
            'bright-white': '\x1b[107m',
        },
        paint(text, fore, back) {
            if (fore) {
                text = this.foregroundColors[fore] + text
            }
            if (back) {
                text = this.backgroundColors[back] + text
            }
            if (fore || back) {
                text += '\x1b[0m'
            }
            return text
        }
    }
}

main()