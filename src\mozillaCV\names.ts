
export const girls = '<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON><PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON><PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON><PERSON><PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON><PERSON>,<PERSON>,<PERSON><PERSON>,<PERSON>,<PERSON>,<PERSON><PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON><PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON><PERSON><PERSON>,<PERSON><PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,Eloise,Madelyn,<PERSON><PERSON><PERSON>,<PERSON>,<PERSON>,<PERSON><PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON><PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON><PERSON>,<PERSON><PERSON>,<PERSON>,<PERSON><PERSON>,<PERSON>,<PERSON><PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON><PERSON><PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>'.split(',')
export const boys = '<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON><PERSON><PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON><PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON><PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON><PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON><PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON><PERSON>,<PERSON>,<PERSON>on,<PERSON>,<PERSON>,<PERSON><PERSON>,<PERSON>'.split(',').filter(boy => !girls.includes(boy))

console.log(girls.length, "x", boys.length)

export function random<PERSON><PERSON>(gender: 'male' | 'female', id: string) {
    const isMale = gender === 'male'
    const names = isMale ? boys : girls
    const lastNames = isMale ? girls : boys
    return names[simpleHash(id) % names.length] + ' ' + lastNames[simpleHash(id) % lastNames.length]
}

function simpleHash(str: string) {
    let hash = 0
    for (let i = 0; i < str.length; i++) {
        hash = ((hash << 5) - hash) + str.charCodeAt(i)
        hash |= 0
    }
    return Math.abs(hash)
}