"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Utils = exports.getProbe = exports.getEffectPath = exports.killProcess = exports.uuid = void 0;
const path_1 = __importDefault(require("path"));
const promises_1 = __importDefault(require("fs/promises"));
const fs_1 = __importDefault(require("fs"));
const child_process_1 = require("child_process");
const emptyProject_1 = require("../capcut/emptyProject");
const crypto_1 = __importDefault(require("crypto"));
const fluent_ffmpeg_1 = __importDefault(require("fluent-ffmpeg"));
const uuid = () => crypto_1.default.randomUUID().toUpperCase();
exports.uuid = uuid;
const killProcess = async (name) => {
    try {
        await new Promise((resolve, reject) => {
            (0, child_process_1.exec)(`taskkill /F /IM ${name}`, (error, stdout, stderr) => {
                if (error) {
                    reject(error);
                }
                else {
                    resolve();
                }
            });
        });
        console.log(`${name} process killed successfully`);
    }
    catch (error) {
        if (error.message.includes('not found')) {
            return;
        }
        console.error(`${error}`);
    }
};
exports.killProcess = killProcess;
const getEffectPath = async (effectId) => {
    const r = path_1.default.join(emptyProject_1.CapCutEffectDir, effectId);
    let dir = (await promises_1.default.readdir(r)).filter(d => {
        const p = path_1.default.join(r, d);
        //  check if p is a directory
        return fs_1.default.statSync(p).isDirectory();
    })[0];
    if (dir) {
        dir = path_1.default.join(r, dir);
    }
    else {
        dir = r;
    }
    return dir;
};
exports.getEffectPath = getEffectPath;
const getProbe = (mediaPath) => {
    return new Promise((resolve, reject) => {
        fluent_ffmpeg_1.default.ffprobe(mediaPath, (err, data) => {
            if (err) {
                reject(err);
            }
            else {
                resolve(data);
            }
        });
    });
};
exports.getProbe = getProbe;
exports.Utils = {
    shuffleArray(array) {
        for (let i = array.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [array[i], array[j]] = [array[j], array[i]];
        }
        return array;
    },
    sleep(timeOut) {
        return new Promise(resolve => {
            setTimeout(resolve, timeOut);
        });
    },
    randomString(length) {
        if (length < 1) {
            throw new Error('Invalid parameter length < 1');
        }
        let chars = normalChars;
        return new Array(length).fill('0').map(_ => chars[Math.random() * chars.length | 0]).join('');
    },
    randomOtp(length) {
        return new Array(length)
            .fill('0')
            .map(_ => normalChars[10 + Math.random() * 26 | 0])
            .join('');
    },
    async mapAsync(array, mapper, concurrency = 0) {
        concurrency = concurrency | 0;
        concurrency = concurrency <= 0 ? array.length : Math.min(concurrency, array.length);
        let index = 0;
        const results = [];
        const worker = async () => {
            while (index < array.length) {
                const i = index++;
                const r = await mapper(array[i], i, array);
                results[i] = r;
            }
        };
        let concurrent = new Array(concurrency);
        concurrent.fill(0);
        await Promise.all(concurrent.map(_ => worker()));
        return results;
    },
    async runSequence(key, runner, timeStepMs = 0, maxTimeStepMs = 0, distribution = 1) {
        let this_ = exports.Utils;
        let cache = this_.runCache = this_.runCache || {};
        let pending = cache[key];
        if (!pending) {
            let result;
            pending = cache[key] = { start: Date.now(), done: false };
            try {
                result = await runner();
            }
            finally {
                pending.done = true;
                // delete cache[key] don't uncomment this line, deleted key cannot check time
            }
            return result;
        }
        if (timeStepMs > 0 && maxTimeStepMs > timeStepMs && distribution >= 0) {
            timeStepMs += Math.round(Math.random()
                * (maxTimeStepMs - timeStepMs)
                * (Math.random() < distribution ? 1 : distribution));
        }
        while (true) {
            pending = cache[key];
            if (!pending)
                break;
            let delayTime = timeStepMs - (Date.now() - pending.start);
            if (!pending.done || delayTime > 0) {
                await exports.Utils.sleep(exports.Utils.clamp(delayTime, 50, 1000));
            }
            else {
                delete cache[key]; // safe to delete as done and passed time step
                break;
            }
        }
        return await exports.Utils.runSequence(key, runner, timeStepMs);
    },
    clamp(value, min, max) {
        return value < min ? min : value > max ? max : value;
    },
    chalk: {
        foregroundColors: {
            'black': '\x1b[30m',
            'red': '\x1b[31m',
            'green': '\x1b[32m',
            'yellow': '\x1b[33m',
            'blue': '\x1b[34m',
            'magenta': '\x1b[35m',
            'cyan': '\x1b[36m',
            'white': '\x1b[37m',
            'bright-black': '\x1b[90m',
            'bright-red': '\x1b[91m',
            'bright-green': '\x1b[92m',
            'bright-yellow': '\x1b[93m',
            'bright-blue': '\x1b[94m',
            'bright-magenta': '\x1b[95m',
            'bright-cyan': '\x1b[96m',
            'bright-white': '\x1b[97m',
            'none': '\x1b[0m'
        },
        backgroundColors: {
            'black': '\x1b[40m',
            'red': '\x1b[41m',
            'green': '\x1b[42m',
            'yellow': '\x1b[43m',
            'blue': '\x1b[44m',
            'magenta': '\x1b[45m',
            'cyan': '\x1b[46m',
            'white': '\x1b[47m',
            'bright-black': '\x1b[100m',
            'bright-red': '\x1b[101m',
            'bright-green': '\x1b[102m',
            'bright-yellow': '\x1b[103m',
            'bright-blue': '\x1b[104m',
            'bright-magenta': '\x1b[105m',
            'bright-cyan': '\x1b[106m',
            'bright-white': '\x1b[107m',
        },
        /**
         * Make rainbow text to print
         * @link https://en.m.wikipedia.org/wiki/ANSI_escape_code#Colors
         * @param text text to transform
         * @param foreground if foreground is provided, make rainbow in background
         * @returns transformed rainbow text
         */
        rainbow(text, foreground) {
            const foregroundCode = foreground ? {
                'black': '\x1b[30m',
                'red': '\x1b[91m',
                'green': '\x1b[32m',
                'yellow': '\x1b[93m',
                'blue': '\x1b[34m',
                'magenta': '\x1b[35m',
                'cyan': '\x1b[36m',
                'white': '\x1b[97m',
            }[foreground] : '';
            const l = text.length;
            const colors = foreground ?
                ['\x1b[41m', '\x1b[43m', '\x1b[102m', '\x1b[106m', '\x1b[104m', '\x1b[105m'].map(c => c + foregroundCode).concat(['\x1b[0m']) :
                ['\x1b[31m', '\x1b[33m', '\x1b[92m', '\x1b[96m', '\x1b[94m', '\x1b[95m', '\x1b[0m'];
            const step = l / (colors.length - 1);
            const indexArr = new Array(colors.length).fill(0);
            for (let i = 1; i < colors.length; i++) {
                indexArr[i] = Math.round(i * step);
            }
            let result = colors[0];
            for (let i = 1; i < colors.length; i++) {
                result += text.substring(indexArr[i - 1], indexArr[i]);
                result += colors[i];
            }
            return result;
        },
        /**
         * Make rainbow text that loop to print
         * @link https://en.m.wikipedia.org/wiki/ANSI_escape_code#Colors
         * @param text text to transform
         * @param foreground if foreground is provided, make rainbow in background
         * @returns transformed rainbow text
         */
        rainbow2(text, foreground, minGroup = 3) {
            const foregroundCode = foreground ? {
                'black': '\x1b[30m',
                'red': '\x1b[91m',
                'green': '\x1b[32m',
                'yellow': '\x1b[93m',
                'blue': '\x1b[34m',
                'magenta': '\x1b[35m',
                'cyan': '\x1b[36m',
                'white': '\x1b[97m',
            }[foreground] : '';
            const colors = foreground ?
                ['\x1b[41m', '\x1b[43m', '\x1b[102m', '\x1b[106m', '\x1b[104m', '\x1b[105m'].map(c => c + foregroundCode) :
                ['\x1b[31m', '\x1b[33m', '\x1b[92m', '\x1b[96m', '\x1b[94m', '\x1b[95m'];
            if (text.length <= colors.length * minGroup) {
                return this.rainbow(text, foreground);
            }
            let direction = 0, i = 0;
            let result = text.split('').reduce((prev, curr) => {
                let last = prev[prev.length - 1];
                if (last.length < minGroup) {
                    last += curr;
                    prev[prev.length - 1] = last;
                }
                else {
                    prev.push(curr);
                }
                return prev;
            }, ['']).map((c) => {
                i += direction;
                if (i === 0)
                    direction = 1;
                if (i === colors.length - 1)
                    direction = -1;
                return colors[i] + c;
            }).join('') + '\x1b[0m';
            return result;
        },
        paint(text, fore, back) {
            if (fore) {
                text = this.foregroundColors[fore] + text;
            }
            if (back) {
                text = this.backgroundColors[back] + text;
            }
            if (fore || back) {
                text += '\x1b[0m';
            }
            return text;
        }
    }
};
const normalChars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz-_'.split('');
//# sourceMappingURL=utils.js.map