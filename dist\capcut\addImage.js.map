{"version": 3, "file": "addImage.js", "sourceRoot": "", "sources": ["../../src/capcut/addImage.ts"], "names": [], "mappings": ";;;;;;AAAA,gDAAuB;AACvB,uDAAqF;AACrF,yCAA8C;AAC9C,+BAA2B;AAC3B,yCAAqC;AAErC;;;;;;GAMG;AACI,MAAM,OAAO,GAAG,KAAK,EAAE,OAAwB,EAAE,QAAgB,EAAE,KAAa,EAAE,QAAgB,EAAE,QAAgB,EAAE,UAAkB,EAAE,EAAE;IAC/I,MAAM,OAAO,GAAG,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAA;IAC7F,MAAM,OAAO,GAAG,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAA;IACpF,IAAI,CAAC,OAAO,IAAI,CAAC,OAAO,EAAE,CAAC;QACvB,MAAM,IAAI,KAAK,CAAC,qBAAqB,QAAQ,iCAAiC,CAAC,CAAA;IACnF,CAAC;IACD,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,SAAS,CAAC,CAAA;IAC3C,MAAM,SAAS,GAAG,OAAO,CAAC,SAAS,CAAA;IAEnC,MAAM,QAAQ,GAAG,IAAA,YAAI,GAAE,CAAA;IACvB,MAAM,MAAM,GAAG,IAAA,iCAAe,GAAE,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAA;IACtD,MAAM,CAAC,EAAE,GAAG,QAAQ,CAAA;IACpB,SAAS,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAA;IAEzB,MAAM,aAAa,GAAG,IAAA,YAAI,GAAE,CAAA;IAC5B,MAAM,WAAW,GAAG,IAAA,iCAAe,GAAE,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAA;IACpE,WAAW,CAAC,EAAE,GAAG,aAAa,CAAA;IAC9B,SAAS,CAAC,iBAAiB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;IAE7C,MAAM,qBAAqB,GAAG,IAAA,YAAI,GAAE,CAAA;IACpC,MAAM,mBAAmB,GAAG,IAAA,iCAAe,GAAE,CAAC,SAAS,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAA;IACjF,mBAAmB,CAAC,EAAE,GAAG,qBAAqB,CAAA;IAC9C,SAAS,CAAC,sBAAsB,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAA;IAE1D,MAAM,OAAO,GAAG,IAAA,YAAI,GAAE,CAAA;IACtB,MAAM,KAAK,GAAG,IAAA,iCAAe,GAAE,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAA;IACnD,KAAK,CAAC,EAAE,GAAG,OAAO,CAAA;IAClB,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;IAE5B,MAAM,OAAO,GAAG,IAAA,YAAI,GAAE,CAAA;IACtB,MAAM,KAAK,GAAG,IAAA,iCAAe,GAAE,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAA;IACnD,KAAK,CAAC,EAAE,GAAG,OAAO,CAAA;IAClB,KAAK,CAAC,IAAI,GAAG,QAAQ,CAAA;IACrB,KAAK,CAAC,aAAa,GAAG,cAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAA;IAC7C,KAAK,CAAC,aAAa,GAAG,EAAE,CAAA;IACxB,KAAK,CAAC,iBAAiB,GAAG,EAAE,CAAA;IAC5B,IAAI,OAAO,EAAE,CAAC;QACV,MAAM,MAAM,GAAG,MAAM,WAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;QACxC,KAAK,CAAC,QAAQ,GAAG,QAAQ,CAAA;QACzB,KAAK,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,CAAA;QAC1B,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAA;QAC5B,KAAK,CAAC,IAAI,GAAG,OAAO,CAAA;IACxB,CAAC;SAAM,CAAC;QACJ,MAAM,KAAK,GAAG,MAAM,IAAA,gBAAQ,EAAC,QAAQ,CAAC,CAAA;QACtC,KAAK,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,QAAS,GAAG,SAAS,CAAC,CAAA;QAC/D,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,KAAM,CAAA;QACrC,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,MAAO,CAAA;QACvC,KAAK,CAAC,IAAI,GAAG,OAAO,CAAA;QACpB,KAAK,CAAC,SAAS,GAAG,IAAI,CAAA;IAC1B,CAAC;IACD,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;IAE5B,MAAM,iBAAiB,GAAG,IAAA,YAAI,GAAE,CAAA;IAChC,MAAM,eAAe,GAAG,IAAA,iCAAe,GAAE,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAA;IACxE,eAAe,CAAC,EAAE,GAAG,iBAAiB,CAAA;IACtC,SAAS,CAAC,iBAAiB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAA;IAEjD,MAAM,KAAK,GAAG,IAAA,mBAAQ,EAAC,OAAO,EAAE,OAAO,EAAE,UAAU,CAAC,CAAA;IACpD,IAAI,KAAK,GAAG,CAAC,EAAE,CAAC;QACZ,KAAK,GAAG,CAAC,CAAA;QACT,SAAS;QACT,MAAM,WAAW,GAAG,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAA;QAC7D,IAAI,WAAW,EAAE,CAAC;YACd,KAAK,GAAG,WAAW,CAAC,gBAAgB,CAAC,KAAK,GAAG,WAAW,CAAC,gBAAgB,CAAC,QAAQ,CAAA;QACtF,CAAC;IACL,CAAC;SAAM,CAAC;QACJ,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,SAAS,CAAC,CAAA;IACzC,CAAC;IAED,MAAM,OAAO,GAAG,OAAO,CAAC,CAAC,CAAC,IAAA,iCAAe,EAAC,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,IAAA,iCAAe,EAAC,OAAO,CAAC,CAAA;IACtF,OAAO,CAAC,EAAE,GAAG,IAAA,YAAI,GAAE,CAAA;IACnB,OAAO,CAAC,WAAW,GAAG,OAAO,CAAA;IAC7B,OAAO,CAAC,mBAAmB,GAAG,CAAC,QAAQ,EAAE,aAAa,EAAE,qBAAqB,EAAE,OAAO,EAAE,iBAAiB,CAAC,CAAA;IAC1G,OAAO,CAAC,gBAAgB,GAAG;QACvB,KAAK,EAAE,QAAQ;QACf,QAAQ,EAAE,QAAQ;KACrB,CAAA;IACD,OAAO,CAAC,gBAAgB,GAAG;QACvB,KAAK,EAAE,KAAK;QACZ,QAAQ,EAAE,QAAQ;KACrB,CAAA;IACD,OAAO,CAAC,gBAAgB,CAAC,MAAM,GAAG,CAAC,CAAA;IACnC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAc,CAAC,CAAA;IAEnC,OAAO,CAAC,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,QAAQ,EAAE,KAAK,GAAG,QAAQ,CAAC,CAAA;IAE/D,OAAO,EAAC,OAAO,EAAE,KAAK,EAAC,CAAA;AAC3B,CAAC,CAAA;AAvFY,QAAA,OAAO,WAuFnB"}