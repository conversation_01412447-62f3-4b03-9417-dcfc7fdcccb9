import { TemplateProject, templateProject } from './templateProject'
import { uuid } from '../libs/utils'

export const addTrack = (project: TemplateProject, type: "video" | "audio" | "effect" | "text", trackIndex: number): TemplateProject['tracks'][0] => {
    const tracks = project.tracks
    if (!tracks[trackIndex]) {
        const track = templateProject().tracks[0]
        track.segments.length = 0
        track.type = type
        track.id = uuid()
        tracks.push(track)
    }
    const track = tracks[trackIndex]
    if (!track || track.type !== type) {
        throw new Error(`Invalid track ${track}, ${track && track.type}`)
    }
    return track
}