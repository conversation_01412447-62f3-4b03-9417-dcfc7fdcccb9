import fs from 'fs'
import pfs from 'fs/promises'
import cp from 'child_process'
import path from 'path'
import difflib from 'difflib'

// extend the difflib.SequenceMatcher class with a custom ratio method
export class CustomSequenceMatcher extends difflib.SequenceMatcher<string> {
    ratio2(ratio?: number) {
        const length: number = (this as any).a.length + (this as any).b.length
        const _ref = this.getMatchingBlocks()
            .filter(x => x[2] > 0)
            .map(x => Math.abs(x[0] - x[1]) / length)
        const diff = _ref.reduce((acc, cur) => acc * cur, 1)
        return ratio === undefined ? this.ratio() - diff : ratio - diff
    }
}

class SubtitleItem {
    start: number
    end: number
    text: string
    next: SubtitleItem | null

    constructor(start: number, end: number, text: string) {
        this.start = start
        this.end = end
        this.text = text
        this.next = null
    }
}

class Sentence {
    start: SubtitleItem
    end: SubtitleItem

    constructor(start: SubtitleItem, end: SubtitleItem) {
        this.start = start
        this.end = end
    }

    get text(): string {
        let current = this.start
        let text = current.text
        while (current !== this.end) {
            current = current.next!
            text += (current.text[0] === '-' ? '' : ' ') + current.text
        }
        if (text.endsWith(Subtitle.endPunctuations[0])) {
            text = text.substring(0, text.length - 1)
        }
        return text
    }

    split(maxLineLength: number, maxLine: number): SubtitleItem[] {
        const text = this.text
        if (text.length <= maxLineLength) {
            return [new SubtitleItem(this.start.start, this.end.end, text)]
        }
        const lines: SubtitleItem[] = []
        let current = this.start
        let start = current
        let lineText = current.text
        while (current !== this.end) {
            current = current.next!
            lineText += (current.text[0] === '-' ? '' : ' ') + current.text
            if (lineText[0] === ' ') {
                lineText = lineText.substring(1)
            }
            if (lineText.length > maxLineLength || current === this.end) {
                if (lineText[lineText.length - 1] === Subtitle.endPunctuations[0]) {
                    lineText = lineText.substring(0, lineText.length - 1)
                }
                lines.push(new SubtitleItem(start.start, current.end, lineText))
                lineText = ''
                start = current
            }
        }
        if (start !== current) {
            lines.push(new SubtitleItem(start.start, current.end, lineText))
        }
        const subs: SubtitleItem[] = []
        while(lines.length) {
            const linesIn = lines.splice(0, maxLine)
            subs.push(new SubtitleItem(linesIn[0].start, linesIn[linesIn.length - 1].end, linesIn.map(x => x.text).join('\n')))
        }
        return subs
    }
    
    splitLongSub(maxLineLength: number, maxLine: number): SubtitleItem[] {
        const text = this.text
        if (text.length <= maxLineLength) {
            return [new SubtitleItem(this.start.start, this.end.end, text)]
        }
        const lines: SubtitleItem[] = []
        let current = this.start
        let start = current
        let lineText = current.text
        while (current !== this.end) {
            const next = current.next!
            const nextText = (next.text[0] === '-' ? '' : ' ') + next.text
            if ((lineText.length + nextText.length) > maxLineLength) {
                if (lineText[lineText.length - 1] === Subtitle.endPunctuations[0]) {
                    lineText = lineText.substring(0, lineText.length - 1)
                }
                lines.push(new SubtitleItem(start.start, current.end, lineText))
                lineText = ''
                start = next
            }
            lineText += nextText
            current = next
        }
        if (lineText) {
            lines.push(new SubtitleItem(start.start, current.end, lineText))
        }
        const subs: SubtitleItem[] = []
        while(lines.length) {
            if (lines[0].text.length > maxLineLength) {
                subs.push(new SubtitleItem(lines[0].start, lines[0].end, splitTextToLines(lines[0].text, maxLineLength)))
                lines.shift()
                continue
            }
            const linesIn = lines.splice(0, maxLine)
            const text = linesIn.map(x => x.text.trim()).join(' ')
            if (text.length < maxLineLength * maxLine * 0.85) {
                subs.push(new SubtitleItem(linesIn[0].start, linesIn[linesIn.length - 1].end, splitTextToLines(text, maxLineLength)))
            } else {
                subs.push(new SubtitleItem(linesIn[0].start, linesIn[0].end, splitTextToLines(linesIn[0].text, maxLineLength)))
                lines.unshift(...linesIn.slice(1))
            }
        }
        return subs
    }
}

const splitTextToLines = (text: string, maxLineLength: number): string => {
    const words = text.split(' ').filter(x => x)
    const lines: string[] = []
    let line = words[0]
    for (let i = 1; i < words.length; i++) {
        const word = words[i]
        const newLine = line + ' ' + word
        if (newLine.length > maxLineLength) {
            lines.push(line)
            line = word
        } else if (i === words.length - 1) {
            lines.push(newLine)
            line = ''
        } else {
            line += ' ' + word
        }
    }
    if (line) {
        lines.push(line)
    }
    return lines.map(x => x.trim()).join('\n')
}

export class Subtitle {
    static endPunctuations = ['`', '.', '!', '?']
    subtitleItems: SubtitleItem[]
    fullText: string
    wordPositions: [number, number][]
    startIndex: number = 0

    constructor(subtitleItems: SubtitleItem[]) {
        this.subtitleItems = subtitleItems
        this.fullText = ""
        this.wordPositions = []
        for (const item of subtitleItems) {
            const text = item.text.replace(/\W/g, '').toLowerCase()
            this.wordPositions.push([this.fullText.length, this.fullText.length + text.length - 1])
            this.fullText += text
        }
    }

    /**
     * Call once before calling findChapterPos from start to reset startIndex
     */
    startFindingChapterPos() {
        this.startIndex = 0
    }

    findChapterPos(chapter: string): SubtitleItem | undefined {
        let maxScore = 0
        let maxIndex = 0
        for (let i = this.startIndex; i < this.fullText.length; i++) {
            const text = this.fullText.substring(i, i + chapter.length)
            if (text.length < chapter.length) {
                break
            }
            // const score = charByCharSimilarity(chapter, text)
            const match = new CustomSequenceMatcher(null, chapter, text)
            const score = match.ratio()
            const score2 = match.ratio2(score)
            if (score2 >= maxScore) {
                maxScore = score
                maxIndex = i
            }
            if (score < 0.3) {
                i += 15
            } else if (score < 0.4) {
                i += 5
            } else if (score < 0.55) {
                i += 2
            }
            if (maxScore > 0.99) {
                break
            }
            if (maxScore > 0.9 && score < 0.85) {
                break
            }
        }
        for (let i = 0; i < this.wordPositions.length; i++) {
            const [start, end] = this.wordPositions[i]
            if (start <= maxIndex && end >= maxIndex) {
                this.startIndex = maxIndex + 1
                return this.subtitleItems[i]
            }
        }
    }

    compact(maxLineLength: number, maxLine: number): SubtitleItem[] {
        const sentences: Sentence[] = []
        const endPunctuations = Subtitle.endPunctuations
        const last = this.subtitleItems[this.subtitleItems.length - 1]
        if (!last.text.endsWith('.')) {
            last.text += '.'
        }
        for (let i = 0; i < this.subtitleItems.length - 1; i++) {
            const start = this.subtitleItems[i]
            for (; i < this.subtitleItems.length; i++) {
                const end = this.subtitleItems[i]
                if (endPunctuations.includes(end.text[end.text.length - 1])) {
                    sentences.push(new Sentence(start, end))
                    break
                }
            }
        }

        const subtitleItems = sentences
            .map(s => s.splitLongSub(maxLineLength, maxLine))
            .flat()

        for (let i = 1; i < subtitleItems.length; i++) {
            const prev = subtitleItems[i - 1]
            const current = subtitleItems[i]
            prev.end = current.start
        }

        return subtitleItems
    }
}

const charByCharSimilarity = (seq1: string, seq2: string): number => {
    const maxLength = Math.max(seq1.length, seq2.length)
    let sameCount = 0
    for (let i = 0; i < maxLength; i++) {
        if (seq1[i] === seq2[i]) {
            sameCount++
        }
    }
    return sameCount / maxLength
}

const readSubtitle = async (srt_path: string): Promise<SubtitleItem[]> => {
    const subtitle: SubtitleItem[] = []
    const srtText = await pfs.readFile(srt_path, 'utf8')
    const entries = srtText.split(/\s*\n\s*\n\s*/)
    const toSeconds = (time: string): number => {
        const parts = time.split(/[:,.]/).map(Number)
        if (parts.length == 2) {
            return parts[0] + parts[1] / 1000
        }
        return parts[0] * 3600 + parts[1] * 60 + parts[2] + parts[3] / 1000
    }
    let last: any = {}
    for (const entry of entries) {
        const lines = entry.split('\n')
        if (lines.length >= 3) {
            const times = lines[1].trim()
            const [start, end] = times.split(/\s*-->\s*/).map(toSeconds)
            const text = lines.slice(2).map(x => x.trim()).join('\n')
            last.next = new SubtitleItem(start, end, text)
            last = last.next
            subtitle.push(last)
        }
    }
    return subtitle
}

const adjustSubtitle = (subtitle: SubtitleItem[], word: boolean, showLog: boolean): SubtitleItem[] => {
    // Remove subtitle that is not in sound clip (often last subtitles generated from silent)
    const emptySubPos = subtitle
        .slice(word ? -15 : -3)
        .filter(x => Number(Math.abs(x.start - x.end).toPrecision(6)) <= (word ? 0 : 0.165))
        .map(x => subtitle.indexOf(x, subtitle.length - 20))

    let oneCount = 0
    for (let i = 1; i < emptySubPos.length; i++) {
        if (emptySubPos[i] - emptySubPos[i - 1] === 1) {
            oneCount++
            if (oneCount === (word ? 2 : 1)) {
                // found subtile generated from silent
                const out = subtitle.splice(emptySubPos[i - (word ? 2 : 1)])
                showLog && console.log(`Removed last sentence "${out.map(x => x.text).join(' ')}" generated from silent${word ? ' (word)' : ' (sentence)'}.`)
                break
            }
        } else {
            oneCount = 0
        }
    }
        
    return subtitle
}

export const readImagesAndChapters = async (assetDir: string) => {
    // find image dir
    const files = await pfs.readdir(assetDir)
    const hasImage = files.filter(f => {
        const p = path.join(assetDir, f)
        if (!fs.statSync(p).isDirectory()) {
            return false
        }
        const files = fs.readdirSync(p)
        return files.some(f => f.endsWith('.jpg') || f.endsWith('.png'))
    })
    const imageDir = hasImage[0]
    if (!imageDir) {
        throw new Error("No image directory found in assets directory " + assetDir)
    }
    // get images full path
    const imageDirPath = path.join(assetDir, imageDir)
    const images = fs.readdirSync(imageDirPath)
        .filter(f => f.endsWith('.jpg') || f.endsWith('.png') || f.endsWith('.jpeg'))
        .map(f => path.join(imageDirPath, f))

    // get chapters
    const chapterPath = fs.readdirSync(assetDir)
        .filter(f => f.startsWith('chapter') && f.endsWith('.txt'))
        .map(f => path.join(assetDir, f))[0]
    if (!chapterPath) {
        throw new Error("No chapter file found in assets directory " + assetDir)
    }
    const lines = (await pfs.readFile(chapterPath, 'utf8'))
        .split('\n')
        .map(l => l.trim().toLowerCase())
        .filter(l => l)
    const chapters: string[] = []
    for (let i = 0; i < lines.length; i++) {
        if (lines[i].includes('----')) {
            ++i
            let firstSentence = lines[i]
            while (firstSentence.length < 150 && lines[i + 1].includes('----') === false) {
                firstSentence += lines[i + 1]
                i++
            }
            chapters.push(firstSentence.substring(0, 150).toLowerCase().replace(/\W/g, ''))
        }
    }
    if (chapters.length !== images.length) {
        throw new Error(`Number of chapters (${chapters.length}) and images (${images.length}) are not equal`)
    }
    return {images, chapters}
}

export const mapImagesToSubtitle = async (assetDir: string, showLog: boolean) => {
    if (!fs.existsSync(path.join(assetDir, 'subtitle.srt_by_word')) || !fs.existsSync(path.join(assetDir, 'subtitle.srt'))) {
        await runSubInPython(assetDir)
    }
    const subtitleItems = await readSubtitle(path.join(assetDir, 'subtitle.srt_by_word'))
    adjustSubtitle(subtitleItems, true, showLog)

    const {images, chapters} = await readImagesAndChapters(assetDir)
    
    const subtitle = new Subtitle(subtitleItems)
    const imageSubtitleMap: [string, SubtitleItem][] = []
    // mapping
    for (let i = 0; i < chapters.length; i++) {
        const chapter = chapters[i]
        const image = images[i]
        const sub = subtitle.findChapterPos(chapter)
        if (sub) {
            imageSubtitleMap.push([image, sub])
            let index = subtitleItems.indexOf(sub)
            if (index > 0) {
                const item = subtitleItems[index - 1]
                if (!Subtitle.endPunctuations.includes(item.text[item.text.length - 1])) {
                    item.text += Subtitle.endPunctuations[0]
                }
            }
        }
    }
    if (imageSubtitleMap[0] && imageSubtitleMap[0][1]) {
        imageSubtitleMap[0][1].start = 0
    }
    imageSubtitleMap.push(['', subtitleItems[subtitleItems.length - 1]])
    const originSubtitleItems = await readSubtitle(path.join(assetDir, 'subtitle.srt'))
    adjustSubtitle(originSubtitleItems, false, showLog)
    return {imageSubtitleMap, subtitle: new Subtitle(originSubtitleItems)}
}

export async function readSubtitleFromAudio(assetDir: string, showLog: boolean) {
    const srtPath = path.join(assetDir, 'subtitle.srt')
    if (!fs.existsSync(srtPath)) {
        await runSubInPython(assetDir)
    }
    const subtitleItems = await readSubtitle(srtPath)
    adjustSubtitle(subtitleItems, false, showLog)
    return new Subtitle(subtitleItems)
}

async function runSubInPython(assetDir: string) {
    const pythonProcess = cp.spawn('python', ['-m', 'joinAndSub', assetDir], {
        stdio: 'inherit'
    })
    
    let exitCode: number | null = null
    await new Promise<void>((resolve) => {
        pythonProcess.on('close', (code) => {
            exitCode = code
            resolve()
        })
    })
    if (exitCode && exitCode > 0 && exitCode <= 3) {
        throw new Error(`Python process exited with code ${exitCode}`)
    }
}

export const magnetImageToSubtitle = (imageSubtitleMap: [string, SubtitleItem][], subtitles: SubtitleItem[]) => {
    let minIndex = 0
    for (let i = 1; i < imageSubtitleMap.length; i++) {
        const markedSub = imageSubtitleMap[i][1]
        let minGap = Infinity
        for (let k = minIndex; k < subtitles.length; k++) {
            const sub = subtitles[k]
            const gap = Math.abs(sub.start - markedSub.start)
            if (gap < minGap) {
                minGap = gap
                minIndex = k
            }
            if (minGap < 2 && gap > 2) {
                break
            }
        }
        const d = subtitles
            .slice(minIndex - 1, minIndex + 2)
            .filter(x => Math.abs(x.start - markedSub.start) <= minGap * 2)
        const e = d
            .map(x => ({x, score: new CustomSequenceMatcher(null, markedSub.text, x.text).ratio2()}))
            .sort((a, b) => b.score - a.score)
            
        markedSub.start = e[0].x.start
    }
}

export async function findVideoClips(assetDir: string) {
    const channelDir = path.dirname(assetDir)
    const listFile = path.join(channelDir, 'videos.txt')
    let videos: string[]
    if (fs.existsSync(listFile)) {
        const removeQuote = (s: string) => s.replace(/^"(.*)"$/, '$1')
        videos = (await pfs.readFile(listFile, 'utf8'))
            .split('\n')
            .filter(l => l.trim())
            .map(l => path.join(removeQuote(l.trim())))
    } else {
        const allFiles = await pfs.readdir(channelDir)
        videos = allFiles
            .filter(f => f.endsWith('.mp4') || f.endsWith('.mov') || f.endsWith('.avi') || f.endsWith('.mkv'))
            .map(f => path.join(channelDir, f))
    }
    const notExists = videos.filter(f => !fs.existsSync(f))
    if (notExists.length) {
        throw new Error(`Files not found: ${notExists.join(', ')}`)
    }
    if (videos.length === 0) {
        throw new Error(`No video files or "videos.txt" file found in ${channelDir}`)
    }
    const narratorImgs = [path.join(assetDir, 'narrator.png'), path.join(channelDir, 'narrator.png')]
    const narratorImg = narratorImgs.find(d => fs.existsSync(d))
    if (!narratorImg) {
        throw new Error(`Narrator image not found in ${narratorImgs.join(' or ')}`)
    }
    return { videos, narratorImg }
}