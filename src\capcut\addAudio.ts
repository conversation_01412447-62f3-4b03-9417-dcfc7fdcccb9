import path from "path"
import { addTrack } from "./addTrack"
import { templateProject, templateSegment } from "./templateProject"
import { getProbe, uuid } from "../libs/utils"
import type { TemplateProject } from "./templateProject"

/**
 * 
 * @param {ReturnType<typeof import('./templateProject').templateProject>} project
 * @param {string} audioPath 
 * @param {number} start value < 0 to append
 * @param {number} trackIndex 
 */
export const addAudio = async (project: TemplateProject, audioPath: string, start: number, trackIndex: number) => {
    const materials = project.materials
    const duration = (await getProbe(audioPath)).format.duration! * 1_000_000

    const audio = templateProject().materials.audios[0]
    audio.id = uuid()
    audio.path = audioPath
    audio.name = path.basename(audioPath)
    audio.duration = duration
    materials.audios.push(audio)
    
    const beat = templateProject().materials.beats[0]
    beat.id = uuid()
    materials.beats.push(beat)

    const placeholder = templateProject().materials.placeholder_infos[0]
    placeholder.id = uuid()
    materials.placeholder_infos.push(placeholder)

    const soundChannelMapping = templateProject().materials.sound_channel_mappings[0]
    soundChannelMapping.id = uuid()
    soundChannelMapping.type = ""
    materials.sound_channel_mappings.push(soundChannelMapping)

    const speed = templateProject().materials.speeds[0]
    speed.id = uuid()
    materials.speeds.push(speed)

    const vocalSeparation = templateProject().materials.vocal_separations[0]
    vocalSeparation.id = uuid()
    materials.vocal_separations.push(vocalSeparation)
    
    const track = addTrack(project, "audio", trackIndex)
    if (start < 0) {
        start = 0
        // append
        const lastSegment = track.segments[track.segments.length - 1]
        if (lastSegment) {
            start = lastSegment.target_timerange.start + lastSegment.target_timerange.duration
        }
    } else {
        start = Math.floor(start * 1_000_000)
    }

    const segment = templateSegment("audio")
    segment.id = uuid()
    segment.material_id = audio.id
    segment.extra_material_refs = [beat.id, placeholder.id, soundChannelMapping.id, speed.id, vocalSeparation.id]
    segment.source_timerange = {
        start: 0,
        duration: duration
    }
    segment.target_timerange = {
        start: start,
        duration: duration
    }
    track.segments.push(segment as any)
    
    project.duration = Math.max(project.duration, start + duration)
}