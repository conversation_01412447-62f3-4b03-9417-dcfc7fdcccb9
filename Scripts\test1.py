#I want you to complete this function to add all imported images to video track, add imported audio mp3 to audio track, the images is continuously placed in the track, set the clip time of each image to 60 seconds. starting of each image add a text+ element and set name of the image as content of text+, make text+ look like a subtitle. add transition between each image. Add a video from assetsDir/effect.mp4 to project then add it to timeline track above the images track, and set Composite mode of the effect video to Screen, Opacity to 50 percent. Duplicate the effect video multiple times so that it covers the whole timeline.

# 1. import all assets to media pool: images, mp3, effect.mp4
# 2. transcript mp3 to srt, read srt to memory
# 3. map srt to images
# 4. add mp3 to audio track
# 5. add text+ to subtitle track based on srt
# 6. add images to video track based on srt
#   6.1. set key frame to transform Zoom effect: 150% to 100%
# 7. add effect.mp4 to video track above images track, 
#   7.1. set composite mode to Screen, 
#   7.2. set Opacity to 50 percent
#   7.3. duplicate effect.mp4 multiple times to cover the whole timeline

import os
import DaVinci

def run(assets_dir: str, effect_file, exportDir: str):
    resolve = DaVinci.resolve
    projectManager = resolve.GetProjectManager()
    project = projectManager.GetCurrentProject()
    if project:
        projectManager.CloseProject(project)
    projectManager.DeleteProject("Hello World")
    project = projectManager.CreateProject("Hello World")
    mediaStorage = resolve.GetMediaStorage()
    mediaPool = project.GetMediaPool()
    timeline = None

    # Import images
    picturesDir = os.path.join(assets_dir, "Pictures")
    image_files = [f for f in os.listdir(picturesDir) if f.lower().endswith(('.png', '.jpg', '.jpeg', '.gif', '.bmp'))]
    image_paths = [os.path.join(picturesDir, f) for f in image_files]
    mediaStorage.AddItemListToMediaPool(image_paths)

    # Import MP3s
    mp3_files = [f for f in os.listdir(assets_dir) if f.lower().endswith('.mp3')]
    mp3_paths = [os.path.join(assets_dir, f) for f in mp3_files]
    mediaStorage.AddItemListToMediaPool(mp3_paths)

    # Import effect video
    mediaStorage.AddItemListToMediaPool([effect_file])
    
    folder = mediaPool.GetRootFolder()
    folder.GetClipList()

    # Create a new timeline
    timeline = mediaPool.CreateEmptyTimeline("Main Timeline")

    # Place images continuously with text+ and transitions
    startFrame = 0
    frameRate = project.GetSetting("timelineFrameRate")
    clipLength = int(60 * float(frameRate))  # 60 seconds per image

    for i, image_file in enumerate(image_files):
        image_clip = mediaPool.FindItems(image_file)[0]
        mediaPool.AppendToTimeline([image_clip])

        # Add text+ for image name
        text_gen = mediaPool.CreateFusionTitle("Text+")
        text_gen.SetProperty("StyledText", image_file)
        text_gen.SetProperty("Size", 0.07)
        text_gen.SetProperty("Font", "Arial")
        text_gen.SetProperty("VerticalJustification", "Bottom")
        mediaPool.AppendToTimeline([text_gen])

        # Add transition if not the first image
        if i > 0:
            timeline.AddTransition("Cross Dissolve", "Video", i, 1)  # Position: after previous clip

    # Add audio track
    if mp3_files:
        audio_clip = mediaPool.FindItems(mp3_files[0])[0]
        mediaPool.AppendToTimeline([audio_clip])

    # Add and duplicate effect video
    effect_clip = mediaPool.FindItems("effect.mp4")[0]
    duration = len(image_files) * 60  # in seconds
    effect_length = int(effect_clip.GetClipProperty("Duration") or 60)
    repeats = duration // effect_length + 1

    for i in range(repeats):
        mediaPool.AppendToTimeline([effect_clip])

    # Composite settings (Note: This depends on available APIs, some of this might need manual Fusion tweaking)
    videoTrackIndex = 2  # Assuming effect video is on top track
    clips = timeline.GetItemListInTrack("video", videoTrackIndex)
    for clip in clips:
        clip.SetProperty("CompositeMode", "Screen")
        clip.SetProperty("Opacity", 0.5)

# run("C:\\Users\\<USER>\\OneDrive\\YoutubeContent\\The Last Straw Stories\\At Our Anniversary Feast, My MIL Declared My Replacement—She Was Mistaken", "C:\MyData\Youtuber\materials\effect1.mp4", "")

def export(exportDir: str):
    resolve = DaVinci.resolve
    projectManager = resolve.GetProjectManager()
    project = projectManager.GetCurrentProject()
    if project:
        projectManager.CloseProject(project)
    projectManager.DeleteProject("Hello World")
    project = projectManager.CreateProject("Hello World")
    ok = projectManager.ExportProject("Hello World", exportDir)
    print(ok)

# export("C:\\Users\\<USER>\\hello")

def importProject(filePath: str):
    resolve = DaVinci.resolve
    projectManager = resolve.GetProjectManager()
    project = projectManager.GetCurrentProject()
    if project:
        projectManager.CloseProject(project)
    ok = projectManager.ImportProject(filePath, "My imported project")
    print(ok)
    
importProject("C:\\Users\\<USER>\\hello.drp")