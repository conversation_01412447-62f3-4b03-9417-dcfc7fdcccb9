"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.openCapCutRender = void 0;
const config_1 = require("../config");
const child_process_1 = __importDefault(require("child_process"));
const fs_1 = __importDefault(require("fs"));
const promises_1 = __importDefault(require("fs/promises"));
const path_1 = __importDefault(require("path"));
const utils_1 = require("../libs/utils");
const tool_1 = require("../tool");
const nameFileNoExt = (name) => name.substring(0, name.lastIndexOf('.'));
async function isRenderDone(name) {
    const outPath = config_1.config.OUTPUT_VIDEO_PATH;
    if (!outPath || !fs_1.default.existsSync(outPath)) {
        console.log('Not configured video output path or not exist');
        return false;
    }
    const files = await promises_1.default.readdir(outPath);
    const videos = [];
    for (const file of files) {
        const statFile = await promises_1.default.stat(path_1.default.join(outPath, file));
        if (!statFile.isFile() || (Date.now() - statFile.birthtime.getTime()) > 30 * 60_000) {
            continue;
        }
        if (file.endsWith('.mp4')) {
            videos.push(file);
        }
    }
    if (!videos.length) {
        return false;
    }
    name = name.slice(0, 50);
    const matching = videos.map(vid => {
        const match = new tool_1.CustomSequenceMatcher(null, name, nameFileNoExt(vid).slice(0, name.length)).ratio2();
        return { vid, match };
    });
    const best = matching.sort((a, b) => b.match - a.match)[0];
    const done = best.match > 0.8;
    if (done) {
        console.log("Render done", best.vid);
    }
    return done;
}
const openCapCutRender = async (name) => {
    child_process_1.default.exec(`start ${config_1.config.CAPCUT_EXECUTABLE_PATH}`);
    console.log("Opened CapCut, wait for rendering...");
    await utils_1.Utils.sleep(10_000);
    const output = config_1.config.OUTPUT_VIDEO_PATH;
    if (output && fs_1.default.existsSync(output)) {
        const expireTime = Date.now() + 10 * 60 * 1000;
        while (Date.now() < expireTime && !await isRenderDone(name)) {
            await utils_1.Utils.sleep(1_000);
        }
        await (0, utils_1.killProcess)('CapCut.exe');
    }
};
exports.openCapCutRender = openCapCutRender;
//# sourceMappingURL=render.js.map