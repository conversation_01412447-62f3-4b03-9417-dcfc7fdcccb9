{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/automation-server/index.ts"], "names": [], "mappings": ";;;;;AAGA,kCAwBC;AA3BD,4CAA0B;AAE1B,MAAM,QAAQ,GAAG,IAAI,GAAG,EAAmB,CAAA;AAC3C,SAAgB,WAAW;IACvB,MAAM,GAAG,GAAG,IAAI,YAAS,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAA;IAChD,GAAG,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC,EAAE,EAAE,EAAE;QACxB,IAAI,OAAO,GAAuB,SAAS,CAAA;QAC3C,EAAE,CAAC,EAAE,CAAC,SAAS,EAAE,CAAC,OAAO,EAAE,EAAE;YACzB,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAgB,CAAA;YACjE,IAAI,IAAI,CAAC,MAAM,KAAK,OAAO,EAAE,CAAC;gBAC1B,OAAO,GAAG,IAAI,CAAC,OAAO,CAAA;gBACtB,IAAI,OAAO,EAAE,CAAC;oBACV,QAAQ,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAA;gBACnD,CAAC;qBAAM,CAAC;oBACJ,OAAO,CAAC,KAAK,CAAC,0BAA0B,EAAE,IAAI,CAAC,CAAA;gBACnD,CAAC;YACL,CAAC;iBAAM,IAAI,OAAO,EAAE,CAAC;gBACjB,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,SAAS,CAAC,IAAI,CAAC,CAAA;YAC1C,CAAC;QACL,CAAC,CAAC,CAAA;QACF,EAAE,CAAC,EAAE,CAAC,OAAO,EAAE,GAAG,EAAE;YAChB,IAAI,OAAO,EAAE,CAAC;gBACV,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;YAC5B,CAAC;YACD,OAAO,GAAG,SAAS,CAAA;QACvB,CAAC,CAAC,CAAA;IACN,CAAC,CAAC,CAAA;AACN,CAAC;AAED,MAAM,OAAO;IAGU;IAAmB;IAF/B,QAAQ,GAAkB,EAAE,CAAA;IAEnC,YAAmB,EAAU,EAAS,EAAa;QAAhC,OAAE,GAAF,EAAE,CAAQ;QAAS,OAAE,GAAF,EAAE,CAAW;IACnD,CAAC;IAED,SAAS,CAAC,IAAS;QACf,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAA;QAC1B,IAAK,IAAY,CAAC,MAAM,CAAC,EAAE,CAAC;YACxB,IAAI,CAAC;gBACA,IAAY,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAA;YAC/B,CAAC;YAAC,OAAO,CAAC,EAAE,CAAC;gBACT,OAAO,CAAC,KAAK,CAAC,gBAAgB,EAAE,MAAM,EAAE,CAAC,CAAC,CAAA;YAC9C,CAAC;QACL,CAAC;aAAM,CAAC;YACJ,OAAO,CAAC,GAAG,CAAC,gBAAgB,EAAE,MAAM,CAAC,CAAA;QACzC,CAAC;IACL,CAAC;IAED,kBAAkB,CAAC,IAAuB;QACtC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAA;IACjC,CAAC;IAED,IAAI,aAAa;QACb,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAA;IAC3B,CAAC;CACJ"}