
from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from fusionscript import Resolve, TimelineItem # type: ignore # pip install fusionscript-stubs

import DaVinciResolveScript

def scriptApp(appName) -> 'Resolve':
    """
    Return script app
    """
    return DaVinciResolveScript.scriptapp(appName)

resolve = scriptApp("Resolve")
if not resolve:
    raise Exception("DaVinci Resolve is not started.")