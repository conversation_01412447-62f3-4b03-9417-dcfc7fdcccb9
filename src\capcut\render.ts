import { config } from "../config"
import cp from 'child_process'
import fs from 'fs'
import pfs from 'fs/promises'
import path from 'path'
import { killProcess, Utils } from "../libs/utils"
import { CustomSequenceMatcher } from "../tool"

const nameFileNoExt = (name: string) => name.substring(0, name.lastIndexOf('.'))

async function isRenderDone(name: string) {
    const outPath = config.OUTPUT_VIDEO_PATH
    if (!outPath || !fs.existsSync(outPath)) {
        console.log('Not configured video output path or not exist')
        return false
    }
    const files = await pfs.readdir(outPath)
    const videos = []
    for (const file of files) {
        const statFile = await pfs.stat(path.join(outPath, file))
        if (!statFile.isFile() || (Date.now() - statFile.birthtime.getTime()) > 30 * 60_000) {
            continue
        }
        if (file.endsWith('.mp4')) {
            videos.push(file)
        }
    }
    if (!videos.length) {
        return false
    }
    name = name.slice(0, 50)
    const matching = videos.map(vid => {
        const match = new CustomSequenceMatcher(null, name, nameFileNoExt(vid).slice(0, name.length)).ratio2()
        return {vid, match}
    })
    const best = matching.sort((a, b) => b.match - a.match)[0]
    const done = best.match > 0.8
    if (done) {
        console.log("Render done", best.vid)
    }
    return done
}

export const openCapCutRender = async (name: string) => {
    cp.exec(`start ${config.CAPCUT_EXECUTABLE_PATH}`)
    console.log("Opened CapCut, wait for rendering...")
    await Utils.sleep(10_000)
    const output = config.OUTPUT_VIDEO_PATH
    if (output && fs.existsSync(output)) {
        const expireTime = Date.now() + 10 * 60 * 1000
        while (Date.now() < expireTime && !await isRenderDone(name)) {
            await Utils.sleep(1_000)
        }
        await killProcess('CapCut.exe')
    }
}
