{"name": "youtuber", "version": "1.0.0", "description": "For YouTuber", "main": "dist/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build": "tsc --outDir dist", "build-dev": "tsc -w", "split": "ts-node ./src/index.ts -split", "dump": "ts-node ./src/index.ts -dump", "watch": "ts-node ./src/index.ts -w -f C:\\Users\\<USER>\\OneDrive\\YoutubeContent\\channels.txt", "check": "ts-node ./src/index.ts -check", "cv": "ts-node ./src/mozillaCV/index.ts \"C:\\Users\\<USER>\\Downloads\""}, "repository": {"type": "git", "url": "git+https://github.com/hainh/Youtuber.git"}, "keywords": [], "author": "", "license": "ISC", "bugs": {"url": "https://github.com/hainh/Youtuber/issues"}, "homepage": "https://github.com/hainh/Youtuber#readme", "dependencies": {"difflib": "^0.2.4", "dotenv": "^17.2.0", "fluent-ffmpeg": "^2.1.3", "jimp": "^1.6.0", "log-timestamp": "^0.3.0", "node-fetch": "^3.3.2", "node-tesseract-ocr": "^2.2.1", "screenshot-desktop": "^1.15.1", "simple-xml-to-json": "^1.2.3", "ts-node": "^10.9.2", "ws": "^8.18.3"}, "devDependencies": {"@types/difflib": "^0.2.7", "@types/fluent-ffmpeg": "^2.1.27", "@types/screenshot-desktop": "^1.12.3", "@types/ws": "^8.18.1", "typescript": "^5.8.3"}}