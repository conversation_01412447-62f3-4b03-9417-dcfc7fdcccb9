# import debugpy

# endpoint = ("127.0.0.1", 5678)
# debugpy.listen(endpoint)
# print(f"Waiting for debugger connection at {endpoint[0]}:{endpoint[1]}...")
# debugpy.wait_for_client()
# print("Debugger connected, start debugging")

from youtube_transcript_api import YouTubeTranscriptApi
from faster_whisper import WhisperModel, BatchedInferencePipeline
import stable_whisper
import pyperclip

import os
import subprocess
import threading

from tkinter import ttk, messagebox, filedialog, Tk

def doHeavyWork(worker):
    return lambda: threading.Thread(target=worker).start()

# Create the main window
root = Tk()
root.title("Video Summarizer")

# Create and pack the YouTube video ID input
video_id_label = ttk.Label(root, text="YouTube Video ID/URL:")
video_id_label.pack()
video_id_entry = ttk.Entry(root, width=50)
video_id_entry.pack()

def get_values(saving=False):
    # api_key = api_key_entry.get()
    # selected_model = model_var.get()
    video_url = video_id_entry.get()
    video_id = video_url.split("=")[1] if "http" in video_url else video_url
    # print(f"API Key: {api_key}")
    # print(f"Selected Model: {selected_model}")
    print(f"Video ID: {video_url}")
    api = YouTubeTranscriptApi()
    transcript = api.fetch(video_id, ["vi", "en"])
    transcript_text = " ".join([t.text for t in transcript])
    if saving:
        file_path = filedialog.asksaveasfilename(defaultextension=".txt", filetypes=[("Text files", "*.txt"), ("All files", "*.*")])
        if file_path:
            with open(file_path, "w", encoding="utf-8") as file:
                file.write(transcript_text)
            messagebox.showinfo("Action Complete", f"Transcript saved to {file_path}")
    else:
        pyperclip.copy("phân tích nội dụng sau, tìm các key quan trọng, xây dựng các hướng dẫn cụ thể và chi tiết:\n" + transcript_text)
        messagebox.showinfo("Action Complete", "Transcript copied to clipboard!\nContent:\n" + transcript_text)


# Create and pack the submit button
save_button = ttk.Button(root, text="Save Transcript", command=doHeavyWork(lambda: get_values(True)))
save_button.pack()
copy_button = ttk.Button(root, text="Copy Transcript", command=doHeavyWork(lambda: get_values(False)))
copy_button.pack()

def generate_srt(audio, srt_file_path):
    # model = stable_whisper.load_faster_whisper("turbo")
    # result = model.transcribe(audio)
    # result.to_srt_vtt(srt_file_path)

    # expected one of: tiny.en, tiny, base.en, base, small.en, small, medium.en, medium, large-v1, large-v2, large-v3, 
    # large, distil-large-v2, distil-medium.en, distil-small.en, distil-large-v3, large-v3-turbo, turbo
    model_size = "small.en"

    model = WhisperModel("turbo", device="cuda", compute_type="float16")
    segments, info = model.transcribe(audio, beam_size=5)
    # batched_model = BatchedInferencePipeline(model=model)
    # segments, info = batched_model.transcribe(audio, max_new_tokens=30, batch_size=8)
    with open(srt_file_path, 'w', encoding='utf-8') as srt_file:
        for i, segment in enumerate(segments, start=1):
            srt_file.write(f"{i}\n")
            start_time = f"{int(segment.start // 3600):02d}:{int(segment.start // 60):02d}:{int(segment.start % 60):02d},{int((segment.start % 1) * 1000):03d}"
            end_time = f"{int(segment.end // 3600):02d}:{int(segment.end // 60):02d}:{int(segment.end % 60):02d},{int((segment.end % 1) * 1000):03d}"
            srt_file.write(f"{start_time} --> {end_time}\n")
            srt_file.write(f"{segment.text.strip()}\n\n")
            
    root.after_idle(lambda: messagebox.showinfo("Action Complete", "SRT file generated successfully!"))

# start_time = f"{int(start // 60):02d}:{int(start % 60):02d}.{int((start % 1) * 1000):03d}"

def join_mp3s():
    root.after_idle(lambda: status_label.config(text="Joining MP3s:"))
    folder_path = filedialog.askdirectory()
    if not folder_path:
        messagebox.showinfo("Not selected folder", "")
        return
    
    path, name = os.path.split(folder_path)
    output_file = os.path.join(path, name + '.mp3')

    # Step 1: List all MP3 files
    mp3_files = [f for f in os.listdir(folder_path) if f.endswith('.mp3')]
    # Convert filename to number and sort
    mp3_files.sort(key=lambda x: int(''.join(filter(str.isdigit, x))))
    
    root.after_idle(lambda: status_label.config(text=f"Joining MP3s: {len(mp3_files)} files"))

    # Step 2: Create a temporary text file listing the MP3 files in FFmpeg concat format
    file_list_path = os.path.join(folder_path, 'file_list.txt')
    with open(file_list_path, 'w', encoding='utf-8') as f:
        for mp3 in mp3_files:
            file_path = os.path.join(folder_path, mp3)
            f.write(f"file '{file_path}'\n")

    # Step 3: Run FFmpeg to concatenate them
    command = [
        'ffmpeg',
        '-y',
        '-f', 'concat',
        '-safe', '0',
        '-i', file_list_path,
        '-c', 'copy',
        output_file
    ]

    subprocess.run(command)

    # Step 4: Clean up temporary file
    os.remove(file_list_path)
    current_text = status_label.cget("text") + " Done, Generating subtitle"
    root.after_idle(lambda: status_label.config(text=current_text))
    try:
        generate_srt(output_file, os.path.join(path, name + '.srt'))
    except Exception as e:
        print(e)
    current_text = status_label.cget("text") + " Done"
    root.after_idle(lambda: status_label.config(text=current_text))
    

join_button = ttk.Button(root, text="Join MP3s", command=doHeavyWork(join_mp3s))
join_button.pack()

status_label = ttk.Label(root, text="")
status_label.pack()

# Start the Tkinter event loop
root.mainloop()

