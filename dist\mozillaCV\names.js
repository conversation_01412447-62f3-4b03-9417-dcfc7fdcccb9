"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.boys = exports.girls = void 0;
exports.randomName = randomName;
exports.girls = '<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON><PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON><PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON><PERSON><PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON><PERSON>,<PERSON>,<PERSON><PERSON>,<PERSON>,<PERSON>,<PERSON><PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON><PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON><PERSON><PERSON>,<PERSON><PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON><PERSON>,<PERSON><PERSON>,<PERSON><PERSON><PERSON>,<PERSON>,<PERSON>,<PERSON><PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON><PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON><PERSON>,<PERSON><PERSON>,<PERSON>,<PERSON><PERSON>,<PERSON>,<PERSON><PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON><PERSON><PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>'.split(',');
exports.boys = '<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON><PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON><PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON><PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON><PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON><PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>'.split(',').filter(boy => !exports.girls.includes(boy));
console.log(exports.girls.length, "x", exports.boys.length);
function randomName(gender, id) {
    const isMale = gender === 'male';
    const names = isMale ? exports.boys : exports.girls;
    const lastNames = isMale ? exports.girls : exports.boys;
    return names[simpleHash(id) % names.length] + ' ' + lastNames[simpleHash(id) % lastNames.length];
}
function simpleHash(str) {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
        hash = ((hash << 5) - hash) + str.charCodeAt(i);
        hash |= 0;
    }
    return Math.abs(hash);
}
//# sourceMappingURL=names.js.map