import fs from 'fs'
import os from 'os'
import path from 'path'
import { addClip } from './addImage'
import { project } from "./emptyProject"
import { addEffect, addEffect0, EffectNameId } from './addEffect'
import { addSubtitle } from './addSubtitle'
import { addTransition } from './addTransition'
import { addAudio } from './addAudio'
import { killProcess, Utils, uuid } from '../libs/utils'
import { effects } from './effects'
import { TemplateProject } from './templateProject'
import { openCapCutRender } from './render'

/**
 * Time is in seconds (start, duration)
 */
export class CapCutProject {
    private project: TemplateProject
    static CapCutProjectDir: string

    constructor() {
        if (!CapCutProject.CapCutProjectDir) {
            throw new Error("CapCutProject.CapCutProjectDir is not set")
        }
        this.project = project()
    }

    get duration(): number {
        return this.project.duration / 1_000_000
    }

    static defaultDir(): string {
        return path.join(os.homedir(), "AppData", "Local", "CapCut", "User Data", "Projects", "com.lveditor.draft").split(path.sep).join(path.posix.sep)
    }

    async addClip(clipPath: string, start: number, startSrc: number, duration: number, trackIndex: number) {
        return await addClip(this.project, clipPath, start, startSrc, duration, trackIndex)
    }

    /**
     * Add an overlay effect to track (above other tracks)
     * @param effectResourceId 
     * @param start 
     * @param duration 
     * @param trackIndex 
     * @returns 
     */
    async addEffect(effectResourceId: keyof typeof effects, start: number, duration: number, trackIndex: number): Promise<TemplateProject['materials']['video_effects'][0]> {
        return await addEffect(this.project, effectResourceId, start, duration, trackIndex)
    }

    /**
     * Add Blending effect to a Video segment
     * @param nameId 
     * @param segment 
     * @param alpha 0 - 1
     * @returns 
     */
    async addEffect0(nameId: EffectNameId, segment: TemplateProject['tracks'][0]['segments'][0], alpha: number) {
        return await addEffect0(this.project, nameId, segment, alpha)
    }

    addSubtitle(text: string, start: number, duration: number, trackIndex: number) {
        addSubtitle(this.project, text, start, duration, trackIndex)
    }

    async addAudio(audio: string, start: number, trackIndex: number) {
        await addAudio(this.project, audio, start, trackIndex)
    }

    async addTransition(videoIndex: number, transitionResourceId: string, duration: number, trackIndex: number) {
        await addTransition(this.project, videoIndex, transitionResourceId, duration, trackIndex)
    }

    /**
     * Export project to CapCut project folder
     * @param name project name
     */
    async export(name: string): Promise<void> {
        await killProcess('CapCut.exe')
        await Utils.sleep(1000)
        const originalName = name
        name = name.substring(0, 50).trim()
        const rootMetaFilePath = path.join(os.homedir(), "AppData", "Local", "CapCut", "User Data", "Projects", "com.lveditor.draft", "root_meta_info.json")
        const projects = JSON.parse(fs.readFileSync(rootMetaFilePath, "utf8"))
        // Delete old project has same name
        projects["all_draft_store"] = projects["all_draft_store"].filter((d: { draft_name: string }) => d.draft_name !== name)
        const existingProjectPath = projects["all_draft_store"][0]["draft_fold_path"]
        const draftEntry = Object.assign({}, projects["all_draft_store"][0], {
            "draft_cover": `${CapCutProject.CapCutProjectDir}/${name}${path.sep}draft_cover.jpg`,
            "draft_fold_path": `${CapCutProject.CapCutProjectDir}/${name}`,
            "draft_id": uuid(),
            "draft_json_file": `${CapCutProject.CapCutProjectDir}/${name}${path.sep}draft_content.json`,
            "draft_name": name,
            "draft_root_path": CapCutProject.CapCutProjectDir,
        })
        projects["all_draft_store"].unshift(draftEntry)
        projects["draft_ids"] = projects["draft_ids"] + 1
        fs.writeFileSync(rootMetaFilePath, JSON.stringify(projects))

        const projectPath = draftEntry["draft_fold_path"]
        fs.mkdirSync(projectPath, { recursive: true })
        // Copy all file in dir existingProjectPath to projectPath
        fs.cpSync(existingProjectPath, projectPath, { recursive: true })
        fs.writeFileSync(draftEntry["draft_json_file"], JSON.stringify(this.project))
        console.log("Exported to", Utils.chalk.paint(projectPath, 'magenta'))
        await Utils.sleep(1000)
        await openCapCutRender(originalName)
    }
}
CapCutProject.CapCutProjectDir = CapCutProject.defaultDir()
