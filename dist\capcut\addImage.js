"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.addClip = void 0;
const path_1 = __importDefault(require("path"));
const templateProject_1 = require("./templateProject");
const utils_1 = require("../libs/utils");
const jimp_1 = require("jimp");
const addTrack_1 = require("./addTrack");
/**
 * @param {TemplateProject} project
 * @param {string} clipPath image or video path
 * @param {number} start -1 to append to track.
 * @param {number} duration
 * @param {number} trackIndex
 */
const addClip = async (project, clipPath, start, startSrc, duration, trackIndex) => {
    const isImage = ['.jpg', '.png', '.jpeg', '.gif', '.bmp'].some(ext => clipPath.endsWith(ext));
    const isVideo = ['.mp4', '.mov', '.avi', '.mkv'].some(ext => clipPath.endsWith(ext));
    if (!isImage && !isVideo) {
        throw new Error(`Invalid clip path ${clipPath}. Only support image and video.`);
    }
    duration = Math.floor(duration * 1_000_000);
    const materials = project.materials;
    const canvasId = (0, utils_1.uuid)();
    const canvas = (0, templateProject_1.templateProject)().materials.canvases[0];
    canvas.id = canvasId;
    materials.canvases.push();
    const placeholderId = (0, utils_1.uuid)();
    const placeholder = (0, templateProject_1.templateProject)().materials.placeholder_infos[0];
    placeholder.id = placeholderId;
    materials.placeholder_infos.push(placeholder);
    const soundChannelMappingId = (0, utils_1.uuid)();
    const soundChannelMapping = (0, templateProject_1.templateProject)().materials.sound_channel_mappings[0];
    soundChannelMapping.id = soundChannelMappingId;
    materials.sound_channel_mappings.push(soundChannelMapping);
    const speedId = (0, utils_1.uuid)();
    const speed = (0, templateProject_1.templateProject)().materials.speeds[0];
    speed.id = speedId;
    materials.speeds.push(speed);
    const videoId = (0, utils_1.uuid)();
    const video = (0, templateProject_1.templateProject)().materials.videos[0];
    video.id = videoId;
    video.path = clipPath;
    video.material_name = path_1.default.basename(clipPath);
    video.category_name = '';
    video.local_material_id = '';
    if (isImage) {
        const jImage = await jimp_1.Jimp.read(clipPath);
        video.duration = duration;
        video.width = jImage.width;
        video.height = jImage.height;
        video.type = 'photo';
    }
    else {
        const probe = await (0, utils_1.getProbe)(clipPath);
        video.duration = Math.floor(probe.format.duration * 1_000_000);
        video.width = probe.streams[0].width;
        video.height = probe.streams[0].height;
        video.type = 'video';
        video.has_audio = true;
    }
    materials.videos.push(video);
    const vocalSeparationId = (0, utils_1.uuid)();
    const vocalSeparation = (0, templateProject_1.templateProject)().materials.vocal_separations[0];
    vocalSeparation.id = vocalSeparationId;
    materials.vocal_separations.push(vocalSeparation);
    const track = (0, addTrack_1.addTrack)(project, "video", trackIndex);
    if (start < 0) {
        start = 0;
        // append
        const lastSegment = track.segments[track.segments.length - 1];
        if (lastSegment) {
            start = lastSegment.target_timerange.start + lastSegment.target_timerange.duration;
        }
    }
    else {
        start = Math.floor(start * 1_000_000);
    }
    const segment = isImage ? (0, templateProject_1.templateSegment)("video", "still") : (0, templateProject_1.templateSegment)("video");
    segment.id = (0, utils_1.uuid)();
    segment.material_id = videoId;
    segment.extra_material_refs = [canvasId, placeholderId, soundChannelMappingId, speedId, vocalSeparationId];
    segment.source_timerange = {
        start: startSrc,
        duration: duration
    };
    segment.target_timerange = {
        start: start,
        duration: duration
    };
    segment.common_keyframes.length = 0;
    track.segments.push(segment);
    project.duration = Math.max(project.duration, start + duration);
    return { segment, video };
};
exports.addClip = addClip;
//# sourceMappingURL=addImage.js.map