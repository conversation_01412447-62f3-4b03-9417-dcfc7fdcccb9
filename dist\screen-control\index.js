"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.dumpScreen = dumpScreen;
const screenshot_desktop_1 = __importDefault(require("screenshot-desktop"));
const node_tesseract_ocr_1 = __importDefault(require("node-tesseract-ocr"));
const simple_xml_to_json_1 = require("simple-xml-to-json");
async function dumpScreen() {
    try {
        const start = new Date();
        const imgBuf = await (0, screenshot_desktop_1.default)({ format: "png" });
        const scrShot = new Date();
        const text = await node_tesseract_ocr_1.default.recognize(imgBuf, {
            lang: 'eng',
            presets: ['alto'],
            binary: '"C:\\Program Files\\Tesseract-OCR\\tesseract.exe"'
        });
        const end = new Date();
        console.log(`Total: ${end.getTime() - start.getTime()}ms`);
        console.log(`Screenshot: ${scrShot.getTime() - start.getTime()}ms`);
        console.log(`OCR: ${end.getTime() - scrShot.getTime()}ms`);
        return packTesseractData((0, simple_xml_to_json_1.convertXML)(text), 0.5);
    }
    catch (err) {
        console.error("[ERROR]", err);
    }
}
function packTesseractData(json, minConfidence = 0.01) {
    const printSpace = json.alto.children.find((c) => c.Layout)
        .Layout.children.find((c) => c.Page)
        .Page.children.find((c) => c.PrintSpace)
        .PrintSpace.children;
    const cBlocks = printSpace.map((c) => c.ComposedBlock).filter((x) => x);
    const page = {
        blocks: [],
        text: '',
        rect: null,
        elements: []
    };
    page.blocks = cBlocks.map((c) => {
        const lines = c.children.map((d) => d.TextBlock.children)
            .flat()
            .map(l => {
            const elements = l.TextLine.children.filter((d) => d.String).map((d) => {
                const s = d.String;
                const element = {
                    text: s.CONTENT.replaceAll('&gt;', '>').replaceAll('&lt;', '<').replaceAll('&amp;', '&').replaceAll('&quot;', '"').replaceAll('&apos;', "'"),
                    rect: new Rect(+s.HPOS, +s.VPOS, +s.WIDTH, +s.HEIGHT),
                    confidence: +s.WC
                };
                if (element.confidence < minConfidence) {
                    return null;
                }
                page.elements.push(element);
                return element;
            }).filter(x => x);
            if (!elements.length) {
                return null;
            }
            const line = {
                text: elements.map(e => e.text).join(' '),
                rect: Rect.join(elements.map(e => e.rect)),
                words: elements
            };
            return line;
        }).filter(x => x);
        if (!lines.length) {
            return null;
        }
        const block = {
            text: lines.map(l => l.text).join('\n'),
            rect: Rect.join(lines.map(e => e.rect)),
            lines: lines
        };
        return block;
    }).filter(x => x);
    page.rect = Rect.join(page.blocks.map(e => e.rect));
    page.text = page.blocks.map(b => b.text).join('\n');
    return page;
}
class Rect {
    x;
    y;
    width;
    height;
    constructor(x, y, width, height) {
        this.x = x;
        this.y = y;
        this.width = width;
        this.height = height;
    }
    join(other) {
        return new Rect(Math.min(this.x, other.x), Math.min(this.y, other.y), Math.max(this.x + this.width, other.x + other.width) - Math.min(this.x, other.x), Math.max(this.y + this.height, other.y + other.height) - Math.min(this.y, other.y));
    }
    static join(rects) {
        return rects.reduce((acc, cur) => acc.join(cur), rects[0] || new Rect(0, 0, 0, 0));
    }
}
//# sourceMappingURL=index.js.map