
import os
import re
import subprocess
from typing import Tuple
from tqdm import tqdm
from time import sleep
# import gc
# import torch

class Segment:
    def __init__(self, start, end, text, words):
        self.start = float(start)
        self.end = float(end)
        if words:
            self.text = text
            self.words = [Word(w["start"], w["end"], w["word"]) for w in words]
        else:
            self.word = text.strip()
            self.words = None
    def __repr__(self):
        return f"Segment(start={self.start}, end={self.end}, text={self.text})"
    
class Word(Segment):
    def __init__(self, start, end, word):
        super().__init__(start, end, word, None)
    def __repr__(self):
        return f"Word(start={self.start}, end={self.end}, word={self.word})"

MAX_CHAR = 20

class SubtitleItem:
    def __init__(self, start, end, text):
        self.start = start
        self.end = end
        self.text = text
        self.raw_text = "".join(re.findall(r"\w", text)).lower()
        
    def __repr__(self):
        return f"SubtitleItem(start={self.start}, end={self.end}, text={self.text})"

def adjust_subtitle(subtitle: list[SubtitleItem], extendMs: int = 100):
    """
    Adjust subtitles to extend start end end by extendMs, ensure no overlap.
    """
    if extendMs == 0:
        return
    extendMs /= 1000
    subtitle[0].start = max(0, subtitle[0].start - extendMs)
    for i, sub in enumerate(subtitle[1:], start=1):
        prev_sub = subtitle[i - 1]
        prev_sub.end += extendMs
        sub.start -= extendMs
        if sub.start < prev_sub.end:
            mid = (sub.start + prev_sub.end) // 2
            prev_sub.end = mid
            sub.start = mid

def join_mp3s(folder_path, normalizeVolume=False):
    """
    Join all MP3 files in its subdirectory into a single MP3 file.
    Skip if there is an MP3 file in the folder.
    """
    files_current_dir = os.listdir(folder_path)
    # check if there is a mp3 file
    mp3_files_path = [f for f in files_current_dir if f.endswith('.mp3')]
    if mp3_files_path:
        print(f"An MP3 file found in the folder: {mp3_files_path[0]}. Skipped it. Deleting it to try again.")
        return
    # List all subdirectories in the folder
    parts_path: str = None
    subdirectories = [d for d in files_current_dir if os.path.isdir(os.path.join(folder_path, d))]
    for subdirectory in subdirectories:
        files: list[str] = os.listdir(os.path.join(folder_path, subdirectory))
        for file in files:
            if file.endswith('.mp3'):
                parts_path = os.path.join(folder_path, subdirectory)
                break
    if not parts_path:
        print("No MP3 part files found in subdirectories.")
        return
    
    _, name = os.path.split(parts_path)
    output_file = os.path.join(folder_path, name + '.mp3')

    # Step 1: List all MP3 files
    mp3_files = [f for f in os.listdir(parts_path) if f.endswith('.mp3')]
    # Convert filename to number and sort
    mp3_files.sort(key=lambda x: int(''.join(filter(str.isdigit, x))))
    
    if normalizeVolume:
        print(f"Normalizing MP3s from subdirectory: {parts_path}, {len(mp3_files)} files")
        wav_files = []
        with tqdm(total=len(mp3_files), unit='file', desc="Normalizing  ") as tqdm_pbar:
            for mp3 in mp3_files:
                wav = mp3.replace(".mp3", ".wav")
                wav_files.append(wav)
                command = [
                    'ffmpeg',
                    '-y',
                    '-i', os.path.join(parts_path, mp3),
                    '-af', 'loudnorm', '-ar', '44100',
                    os.path.join(parts_path, wav)
                ]
                result = subprocess.run(command, stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
                tqdm_pbar.update(1)
                tqdm_pbar.set_description(f"Normalizing ({mp3})")
                if result.returncode:
                    raise Exception(f"Run command error: {command}")
    else:
        wav_files = mp3_files
        
    print("Joining MP3s...")
    # Step 2: Create a temporary text file listing the MP3 files in FFmpeg concat format
    file_list_path = os.path.join(parts_path, 'file_list.txt')
    with open(file_list_path, 'w', encoding='utf-8') as f:
        for wav in wav_files:
            file_path = os.path.join(parts_path, wav)
            f.write(f"file '{file_path}'\n")

    # Step 3: Run FFmpeg to concatenate them
    command = [
        'ffmpeg',
        '-y',
        '-f', 'concat',
        '-safe', '0',
        '-i', file_list_path,
        *(['-c:a', 'libmp3lame', '-b:a', '192k'] if normalizeVolume else ['-c', 'copy']),
        # # concat mp3s
        # '-c', 'copy',
        # #
        # # concat wavs then convert to mp3
        # '-c:a', 'libmp3lame', '-b:a', '192k',
        # #
        output_file
    ]
    result = subprocess.run(command, stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
    if result.returncode:
        raise Exception(f"Run command error: {command}")

    # Step 4: Clean up temporary file
    os.remove(file_list_path)
    # delete all wavs
    if normalizeVolume:
        for wav in wav_files:
            os.remove(os.path.join(parts_path, wav))
    print("MP3 files joined successfully.")

def transcribe(audio_file_path) -> list[SubtitleItem]:
    """
    Transcribe audio file to SRT file.
    If SRT file exists, skip it.
    """
    if not os.path.isfile(audio_file_path):
        raise Exception("No audio file found in the folder.")
    folder_path = os.path.dirname(audio_file_path)
    srt_file_path = [f for f in os.listdir(folder_path) if f.endswith('subtitle.srt') or f.endswith("subtitle.srt_by_word")]
    if len(srt_file_path) == 2:
        srt_file_path = srt_file_path[0]
        print(f"An SRT file found in the folder: {srt_file_path}. Skipped generating subtitle.")
    else:
        srts = srt_file_path
        srt_file_path = "subtitle.srt"
        print("Generating SRT file...")
        # # Using faster-whisper
        # import faster_whisper
        # model = faster_whisper.WhisperModel("turbo")
        # segmentsIter, info = model.transcribe(audio_file_path, beam_size=5, word_timestamps=True)
        # duration = float(info.duration)
        # segments = []
        # with tqdm(total=duration, unit='sec', desc="Transcribe") as tqdm_pbar:
        #     for segment in segmentsIter:
        #         segment = segment._asdict()
        #         segments.append(Segment(segment["start"], segment["end"], segment["text"], segment["words"]))
        #         tqdm_pbar.update(float(segment["end"] - tqdm_pbar.n))
        #     tqdm_pbar.update(duration - tqdm_pbar.n)
        
        # Using stable-whisper
        import stable_whisper
        model = stable_whisper.load_faster_whisper("turbo")
        result = model.transcribe(audio_file_path)
        segments = result.segments
        
        if srt_file_path not in srts:
            with open(os.path.join(folder_path, srt_file_path), 'w', encoding='utf-8') as srt_file:
                for i, segment in enumerate(segments, start=1):
                    srt_file.write(f"{i}\n")
                    start_time = f"{int(segment.start // 3600):02d}:{int(segment.start // 60 % 60):02d}:{int(segment.start % 60):02d},{int((segment.start % 1) * 1000):03d}"
                    end_time = f"{int(segment.end // 3600):02d}:{int(segment.end // 60 % 60):02d}:{int(segment.end % 60):02d},{int((segment.end % 1) * 1000):03d}"
                    srt_file.write(f"{start_time} --> {end_time}\n")
                    srt_file.write(f"{segment.text.strip()}\n\n")
            print(f"{srt_file_path} generated successfully!")
        with open(os.path.join(folder_path, "subtitle.srt_by_word"), 'w', encoding='utf-8') as srt_file:
            counter = 0
            for i, segment in enumerate(segments, start=1):
                for word in segment.words:
                    counter += 1
                    srt_file.write(f"{counter}\n")
                    srt_file.write(f"{str(word.start)}-->{str(word.end)}\n")
                    srt_file.write(f"{word.word.strip()}\n\n")
        print("SRT by word file generated successfully!")
        
    def toMs(time: str, pos: str):
        parts = re.compile(r":|,|\.").split(time)
        if len(parts) != 4:
            raise Exception("Invalid time format at subtitle number: " + pos)
        ms = (int(parts[0]) * 3_600
            + int(parts[1]) * 60
            + int(parts[2])
            + int(parts[3]) / 1000)
        return ms
        
    srt_file_path = os.path.join(folder_path, srt_file_path)
    subtitle: list[SubtitleItem] = []
    with open(srt_file_path, 'r', encoding='utf-8') as f:
        data = f.read().strip().split('\n\n')
        for entry in data:
            lines = entry.split('\n')
            if len(lines) >= 3:
                times = lines[1].strip()
                start, end = [toMs(d, lines[0]) for d in times.split(' --> ')]
                text = '\n'.join(lines[2:])
                subtitle.append(SubtitleItem(start, end, text))
    adjust_subtitle(subtitle)
    return subtitle

def map_images_to_subtitle(subtitle: list[SubtitleItem], folder_path: str) -> list[Tuple[str, SubtitleItem]]:
    """
    Map images to subtitle.
    """
    image_subtitle_map: list[Tuple[str, SubtitleItem]] = []
    images: list[str] = []
    for sub in [d for d in os.listdir(folder_path) if os.path.isdir(os.path.join(folder_path, d))]:
        sub_dir = os.path.join(folder_path, sub)
        images = [os.path.join(folder_path, sub, f) for f in os.listdir(sub_dir) if f.lower().endswith(('.png', '.jpg', '.jpeg', '.gif', '.bmp'))]
        if images:
            break
    
    if not images:
        raise Exception("No images found in the folder.")
    
    # read chapters in file chapters.txt
    chapter_path = [f for f in os.listdir(folder_path) if f.startswith('chapter') and f.endswith('.txt')]
    if not chapter_path:
        raise Exception(f"No \"chapter[...].txt\" found in {folder_path}.")
    chapters = []
    with open(os.path.join(folder_path, chapter_path[0]), 'r', encoding='utf-8') as f:
        lines = [l.strip().lower() for l in f.read().split('\n') if l.strip()]
        length = len(lines)
        for i, line in enumerate(lines):
            if "----" in line and i < length - 1:
                first_sentence = lines[i + 1].split(".")[0]
                chapters.append("".join(re.findall(r"\w", first_sentence))[:MAX_CHAR])
    
    if len(images) != len(chapters):
        raise Exception(f"Number of images ({len(images)}) does not match number of chapters ({len(chapters)}). Please check your {folder_path}/{chapter_path[0]} file.")
    
    sub_start = 0
    for i, chapter in enumerate(chapters):
        image = images[i]
        ok = False
        # find subtitle to chapter
        for j, sub in enumerate(subtitle[sub_start:]):
            if chapter in sub.raw_text or sub.raw_text in chapter:
                image_subtitle_map.append((image, sub))
                ok = True
                sub_start = j + sub_start + 1
                break
        if not ok:
            print(f"Finding Chapter/Image {i} in slow mode...")
            max_score = 0
            index = 0
            ## TODO: Need to optimize this
            # for j, sub in enumerate(subtitle[sub_start:]):
            #     score = difflib.SequenceMatcher(None, chapter, sub.raw_text).ratio()
            #     if score > max_score:
            #         max_score = score
            #         index = j
            index = index + sub_start
            image_subtitle_map.append((image, subtitle[index]))
            print(f"WARNING: Chapter/Image {i} ({image}/{chapter}) is not found in subtitle.")

    return image_subtitle_map

# file_path = "C:\\Users\\<USER>\\OneDrive\\YoutubeContent\\Whispers of Life\He Took a $200K Loan in My Name. At Divorce\\He Took a $200K Loan in My Name. At Divorce, He Said Check the Balance. I Said Verify the Signature.mp3"
# subs = transcribe(file_path)
# image_map = map_images_to_subtitle(subs, os.path.dirname(file_path))
# from pprint import pprint
# pprint(image_map)
