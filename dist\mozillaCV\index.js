"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const promises_1 = __importDefault(require("fs/promises"));
const path_1 = __importDefault(require("path"));
const fs_1 = __importDefault(require("fs"));
const node_fetch_1 = __importDefault(require("node-fetch"));
const tsv_1 = require("../libs/tsv");
const names_1 = require("./names");
async function main() {
    const argv = process.argv.slice(2);
    console.log("ARGV", argv);
    const dirs = await findDir(argv[0]);
    const db = [];
    for (let dir of dirs) {
        await readDB(dir, db);
        console.log("DB", db.length, dir);
    }
    const result = db.filter(x => (x.accents || '').includes('United States English'));
    console.log("RESULT", result.length, result);
}
async function findDir(root, dirs = []) {
    const files = await promises_1.default.readdir(root);
    if (files.some(x => x === 'validated.tsv')) {
        dirs.push(root);
    }
    for (let file of files) {
        const p = path_1.default.join(root, file);
        if (fs_1.default.statSync(p).isDirectory()) {
            await findDir(p, dirs);
        }
    }
    return dirs;
}
async function readDB(dir, db = []) {
    // const language = path.basename(dir)
    const tsvPath = path_1.default.join(dir, 'validated.tsv');
    const data = await (0, tsv_1.tsvToJson)(tsvPath);
    data.forEach(x => {
        // x.language = language
        x.up_votes = +x.up_votes;
        x.down_votes = +x.down_votes;
        x.gender = x.gender.split('_')[0];
        x.name = (0, names_1.randomName)(x.gender, x.client_id);
        delete x.client_id;
        delete x.sentence_id;
        for (const k in x) {
            if (x[k] === '') {
                delete x[k];
            }
        }
        db.push(x);
    });
    return db;
}
async function TestFetch() {
    try {
        const params = {
            text: `I was wide awake when it happened.
Not from caffeine.
Not from nerves.
But from pain—the kind that lingers after surgery.
Just three days ago, doctors removed a tumor from my abdomen. I was recovering at home, alone in the quiet, with nothing but soft lighting, the hum of a white noise machine, and a stubborn ache that refused to let me sleep.`,
            text_lang: 'en',
            ref_audio_path: "C:\\MyData\\refAudio.mp3",
            prompt_text: "I invited you to my Halloween party specifically this morning and you aren't here, and that's really rude.",
            prompt_lang: 'en',
            // media_type: 'aac',
        };
        const response = await (0, node_fetch_1.default)('http://localhost:9880/tts?' + new URLSearchParams(params));
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        const data = await response.arrayBuffer();
        fs_1.default.writeFileSync('test.wav', Buffer.from(data));
        console.log('Fetched data:', data);
    }
    catch (error) {
        console.error('Error fetching data:', error);
    }
}
TestFetch();
// main()
//# sourceMappingURL=index.js.map