import type { TemplateProject } from "./templateProject"

export const effects = {
    "7399466759058918662": {
        name: "By the Fireplace",
        adjust_params: [
            {
                "effect_key": "effects_adjust_speed",
                "default": 0.2,
                "value": 0.1,
                "min": 0.0,
                "max": 1.0
            },
            {
                "effect_key": "sticker",
                "default": 1.0,
                "value": 0.4,
                "min": 0.0,
                "max": 1.0
            },
            {
                "effect_key": "effects_adjust_filter",
                "default": 0.4,
                "value": 0.0,
                "min": 0.0,
                "max": 1.0
            }
        ]
    }
}

/**
 *  {[
          {
            "default_value": 0.2,
            "name": "effects_adjust_speed",
            "value": 0.2
          },
          {
            "default_value": 1,
            "name": "sticker",
            "value": 1
          },
          {
            "default_value": 0.4,
            "name": "effects_adjust_filter",
            "value": 0.4
          }
        ]} effect 
 */

export const setEffect = (effect: TemplateProject['materials']['video_effects'][0], effectId: keyof typeof effects) => {
    const effectParams = effects[effectId]
    if (!effectParams) return
    effect.adjust_params = effectParams.adjust_params.map(p => ({
        default_value: p.default,
        name: p.effect_key,
        value: p.value
    }))
    effect.name = effectParams.name
}

export type EffectId = keyof typeof effects