#I want you to complete this function to add all imported images to video track, add imported audio mp3 to audio track, the images is continuously placed in the track, set the clip time of each image to 60 seconds. starting of each image add a text+ element and set name of the image as content of text+, make text+ look like a subtitle. add transition between each image. Add a video from assetsDir/effect.mp4 to project then add it to timeline track above the images track, and set Composite mode of the effect video to Screen, Opacity to 50 percent. Duplicate the effect video multiple times so that it covers the whole timeline.

# 1. import all assets to media pool: images, mp3, effect.mp4
# 2. transcript mp3 to srt, read srt to memory
# 3. map srt to images
# 4. add mp3 to audio track
# 5. add text+ to subtitle track based on srt
# 6. add images to video track based on srt
#   6.1. set key frame to transform Zoom effect: 150% to 100%
# 7. add effect.mp4 to video track above images track, 
#   7.1. set composite mode to Screen, 
#   7.2. set Opacity to 50 percent
#   7.3. duplicate effect.mp4 multiple times to cover the whole timeline

import os
# import DaVinci
from audio import join_mp3s, transcribe, map_images_to_subtitle

def run(assets_dir: str, effect_file, exportDir: str):
    join_mp3s(assets_dir)
    mp3_files = [f for f in os.listdir(assets_dir) if f.lower().endswith('.mp3')]
    if not mp3_files:
        raise Exception("No MP3 file found in the folder.")
    if len(mp3_files) > 1:
        raise Exception("More than one MP3 file found in the folder.")
    mp3_path = os.path.join(assets_dir, mp3_files[0])
    subs = transcribe(mp3_path)
    image_sub = map_images_to_subtitle(subs, assets_dir)
    image_paths = [i[0] for i in image_sub]
    template_file_path = os.path.join(
        os.path.dirname(
            os.path.dirname(os.path.abspath(__file__))),
        "template",
        "stories.drp")
    
    resolve = DaVinci.resolve
    projectManager = resolve.GetProjectManager()
    project = projectManager.GetCurrentProject()
    if project:
        projectManager.CloseProject(project)
    projectName = os.path.split(assets_dir)[1]
    projectManager.DeleteProject(projectName)
    ok = projectManager.ImportProject(template_file_path, projectName)
    if not ok:
        raise Exception(f"Failed to import template project: {projectName}")
    project = projectManager.LoadProject(projectName)
    if not project:
        raise Exception(f"Failed to load project: {projectName}")
    mediaStorage = resolve.GetMediaStorage()
    mediaPool = project.GetMediaPool()

    # Import images
    mediaStorage.AddItemListToMediaPool(image_paths)

    # Import MP3s
    mediaStorage.AddItemListToMediaPool(mp3_path)

    # Import effect video
    mediaStorage.AddItemListToMediaPool([effect_file])
    
    folder = mediaPool.GetRootFolder()
    mediaPoolItem = folder.GetClipList()
    for item in mediaPoolItem:
        print(item.GetClipProperty())
        
    print("\n\n")

    # Create a new timeline
    timeline = mediaPool.CreateEmptyTimeline("Main Timeline")
    # vTracks = timeline.GetTrackCount("video") # 1
    # aTracks = timeline.GetTrackCount("audio") # 1
    # tTracks = timeline.GetTrackCount("subtitle") # 0
    timeline.AddTrack("subtitle")
    timeline.AddTrack("video")
    
    start_frame_timeline = timeline.GetStartFrame()
    frameRate = project.GetSetting("timelineFrameRate")
    # Add audio track
    audio_clip = [item for item in mediaPoolItem if item.GetClipProperty("Clip Name") == mp3_files[0]]
    audio_in_timeline = mediaPool.AppendToTimeline(audio_clip)[0]
    start_frame = audio_in_timeline.GetStart()
    end_frame = audio_in_timeline.GetEnd()
    duration = audio_in_timeline.GetDuration(False)

    # Add Images match with subtitles
    for i, (image_file, sub) in enumerate(image_sub):
        start = start_frame + int(sub.start * frameRate)
        end = start_frame + int(image_sub[i + 1][1].start * frameRate) if i < len(image_sub) - 1 else end_frame - start_frame
        d = end - start
        image_clip = [item for item in mediaPoolItem if item.GetClipProperty("File Path") == image_file][0]
        image_clip.SetClipProperty("Duration", d)
        ok = mediaPool.AppendToTimeline([{
            "mediaPoolItem": image_clip[0],
            "startFrame": 0,
            "endFrame": d - 1,
            "trackIndex": 1,
            "recordFrame": start
        }])
        if not ok:
            print(f"Failed to add image to timeline {i} -> {image_file}")
        # Add text+ for image name

    images_in_timeline = timeline.GetItemListInTrack("video", 1)
    for image in images_in_timeline:
        print(image.GetProperty())
        image.SetProperty("Duration", 48)
        image.SetProperty("Opacity", 0.5)
    # Place images continuously with text+ and transitions

    for i, image_file in enumerate(image_paths):
        image_clip = mediaPool.FindItems(image_file)[0]
        mediaPool.AppendToTimeline([image_clip])

        # Add text+ for image name
        text_gen = mediaPool.CreateFusionTitle("Text+")
        text_gen.SetProperty("StyledText", image_file)
        text_gen.SetProperty("Size", 0.07)
        text_gen.SetProperty("Font", "Arial")
        text_gen.SetProperty("VerticalJustification", "Bottom")
        mediaPool.AppendToTimeline([text_gen])

        # Add transition if not the first image
        if i > 0:
            timeline.AddTransition("Cross Dissolve", "Video", i, 1)  # Position: after previous clip

    # Add and duplicate effect video
    effect_clip = mediaPool.FindItems("effect.mp4")[0]
    duration = len(image_files) * 60  # in seconds
    effect_length = int(effect_clip.GetClipProperty("Duration") or 60)
    repeats = duration // effect_length + 1

    for i in range(repeats):
        mediaPool.AppendToTimeline([effect_clip])

    # Composite settings (Note: This depends on available APIs, some of this might need manual Fusion tweaking)
    videoTrackIndex = 2  # Assuming effect video is on top track
    clips = timeline.GetItemListInTrack("video", videoTrackIndex)
    for clip in clips:
        clip.SetProperty("CompositeMode", "Screen")
        clip.SetProperty("Opacity", 0.5)

run("C:\\Users\\<USER>\\OneDrive\\YoutubeContent\\The Last Straw Stories\\At Our Anniversary Feast, My MIL Declared My Replacement—She Was Mistaken", "C:\MyData\Youtuber\materials\effect1.mp4", "")
