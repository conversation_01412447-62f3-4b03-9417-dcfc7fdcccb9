"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.trimVideoInBatch = trimVideoInBatch;
exports.splitVideoByScene = splitVideoByScene;
const fluent_ffmpeg_1 = __importDefault(require("fluent-ffmpeg"));
const path_1 = __importDefault(require("path"));
const promises_1 = __importDefault(require("fs/promises"));
const fs_1 = __importDefault(require("fs"));
const child_process_1 = require("child_process");
const utils_1 = require("./utils");
const tool_1 = require("../tool");
const errorDir = path_1.default.resolve(path_1.default.join('.', 'output', 'ffmpeg-stderr'));
async function sleep(timeOut) {
    return new Promise(resolve => {
        setTimeout(resolve, timeOut);
    });
}
const blackDetectOptions = 'blackdetect=d=0.001:pic_th=0.98';
// Parse ffmpeg stderr to find black frame data
function parseBlackFrames(stderr) {
    const regex = /black_start:(\d+(\.\d+)?)\s+black_end:(\d+(\.\d+)?)/g;
    const matches = [...stderr.matchAll(regex)];
    const blackFrames = [];
    for (const match of matches) {
        blackFrames.push([parseFloat(match[1]), parseFloat(match[3])]);
    }
    return blackFrames;
}
// Detect black frames
async function detectBlackFrames(filePath) {
    if (!fs_1.default.existsSync(errorDir)) {
        await promises_1.default.mkdir(errorDir, { recursive: true });
    }
    return new Promise((resolve, reject) => {
        const filename = path_1.default.basename(filePath).split('.')[0].replaceAll(/\W/g, '').slice(0, 30);
        const stderrFile = path_1.default.join(errorDir, `blacks_${filename}_${Math.random().toString().substring(2, 9)}.txt`);
        (0, child_process_1.exec)(`ffmpeg -i "${filePath}" -vf ${blackDetectOptions} -f null - 2>${stderrFile}`, async (error) => {
            if (error) {
                reject(error);
            }
            else {
                const stderr = await promises_1.default.readFile(stderrFile, 'utf8');
                const probes = await (0, utils_1.getProbe)(filePath);
                const duration = probes.format.duration;
                const blackFrames = parseBlackFrames(stderr);
                resolve({ duration, blackFrames });
            }
        });
    });
}
// Trim video
function trimVideo(inputPath, outputPath, startTrim, endTrim, duration) {
    return new Promise((resolve, reject) => {
        const endTime = endTrim || duration;
        const length = endTime - startTrim;
        (0, fluent_ffmpeg_1.default)(inputPath)
            .setStartTime(startTrim)
            .setDuration(length)
            .output(outputPath)
            .on('end', () => resolve())
            .on('error', reject)
            .run();
    });
}
// Main batch processor
async function trimVideoInBatch(assetDir) {
    const { videos } = await (0, tool_1.findVideoClips)(assetDir);
    const outputDir = path_1.default.dirname(videos[0]);
    for (const file of videos) {
        const filename = path_1.default.basename(file).split('.');
        const outPath = path_1.default.join(outputDir, filename[0] + '_trimmed.' + filename[1]);
        console.log(`Processing ${filename.join('.')}...`);
        try {
            await sleep(100);
            const { blackFrames, duration } = await runTillSuccess(() => detectBlackFrames(file));
            if (blackFrames.length) {
                if (blackFrames.length === 1) {
                    if (Math.abs(duration - blackFrames[0][1]) < Math.abs(duration - blackFrames[0][0])) {
                        blackFrames.push([duration, duration]);
                    }
                    else {
                        blackFrames.unshift([0, 0]);
                    }
                }
                const startTrim = blackFrames[0][1];
                const endTrim = blackFrames[blackFrames.length - 1][0];
                console.log(`➤ Trimming from ${startTrim}s to ${endTrim || duration}s`);
                await trimVideo(file, outPath, startTrim, endTrim, duration);
                await sleep(100);
                await promises_1.default.rm(file, { force: true });
                await sleep(100);
                await promises_1.default.rename(outPath, file);
                console.log(`✅ Saved to ${file}`);
            }
        }
        catch (err) {
            console.error(`❌ Error processing ${file}:`, err.message);
        }
    }
}
async function runTillSuccess(fn, maxRetries = 3, retryDelay = 200) {
    let retries = 0;
    while (retries < maxRetries) {
        try {
            return await fn();
        }
        catch (err) {
            console.error(`❌ Error running ${fn.name}:`, err.message);
            retries++;
            if (retries < maxRetries) {
                console.log(`Retrying in ${retryDelay}ms...`);
                await sleep(retryDelay);
            }
        }
    }
    throw new Error(`Failed to run ${fn.name} after ${maxRetries} retries`);
}
async function findChangeScenePosition(filePath) {
    if (!fs_1.default.existsSync(errorDir)) {
        await promises_1.default.mkdir(errorDir, { recursive: true });
    }
    // ffmpeg -i "" -filter:v "select='gt(scene,0.4)',showinfo" -f null - 2> ffoutput.txt
    return new Promise((resolve, reject) => {
        const filename = path_1.default.basename(filePath).split('.')[0].replaceAll(/\W/g, '').slice(0, 30);
        const stderrFile = path_1.default.join(errorDir, `scenes_${filename}_${Math.random().toString().substring(2, 9)}.txt`);
        (0, child_process_1.exec)(`ffmpeg -i "${filePath}" -filter:v "select='gt(scene,0.5)',showinfo" -f null - 2>${stderrFile}`, async (error) => {
            if (error) {
                reject(error);
            }
            else {
                const probes = await (0, utils_1.getProbe)(filePath);
                const duration = probes.format.duration;
                const stderr = await promises_1.default.readFile(stderrFile, 'utf8');
                const changeScenes = parseChangeScene(stderr);
                changeScenes.push(duration);
                resolve({ changeScenes });
            }
        });
    });
}
function parseChangeScene(stderr) {
    const regex = /pts_time:(\d+\.\d+)/g;
    const matches = [...stderr.matchAll(regex)];
    const changeScenes = [0];
    for (const match of matches) {
        changeScenes.push(parseFloat(match[1]));
    }
    return changeScenes;
}
async function splitVideoByScene(inputPath) {
    const outputDir = path_1.default.dirname(inputPath);
    const filename = path_1.default.basename(inputPath).split('.');
    console.time('findChangeScenePosition ' + filename[0]);
    const { changeScenes } = await findChangeScenePosition(inputPath);
    console.timeEnd('findChangeScenePosition ' + filename[0]);
    for (let i = 1; i < changeScenes.length; i++) {
        const start = changeScenes[i - 1];
        const end = changeScenes[i];
        if (end - start < 4) {
            continue;
        }
        const outputName = filename[0] + `_${i}.` + filename[1];
        const outputPath = path_1.default.join(outputDir, outputName);
        if (fs_1.default.existsSync(outputPath)) {
            continue;
        }
        const label = `trimVideo ${outputName}[${start}-${end}]`;
        console.time(label);
        await trimVideo(inputPath, outputPath, start + 0.1, end - 0.1, 0);
        console.timeEnd(label);
    }
}
//# sourceMappingURL=trimVideo.js.map