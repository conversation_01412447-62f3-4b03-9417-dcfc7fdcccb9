"""
Check if video is made and uploaded to Drive
"""

import os
import difflib

root_dir = os.path.dirname(__file__)
channel_name = os.path.basename(root_dir)
uploaded_dir = os.path.join(
    "Z:\\.shortcut-targets-by-id\\1MpLpJw_C919vPotDJfgKGRhtXu1gIr-u\\Jolocat\\Channel",
    channel_name)
video_dir = "C:\\MyData\\Videos"

current_project = [d for d in os.listdir(root_dir) if os.path.isdir(os.path.join(root_dir, d))]
done_videos = [os.path.splitext(d)[0] for d in os.listdir(video_dir) if d.endswith('.mp4')]
days = [os.path.join(uploaded_dir, d) for d in os.listdir(uploaded_dir) if os.path.isdir(os.path.join(uploaded_dir, d))]
uploaded_videos = []
for day in days:
    uploaded_videos.extend([os.path.splitext(d)[0] for d in os.listdir(day) if d.endswith('.mp4')])

result = {}
for project in current_project:
    seqMatcher = [(difflib.SequenceMatcher(None, project, done).ratio(), project, done) for done in done_videos]
    done = max(seqMatcher, key=lambda x: x[0])
    if done[0] > 0.9:
        result[project] = ("HAS VIDEO", "")
    else:
        result[project] = ("         ", "")
        
    seqMatcher = [(difflib.SequenceMatcher(None, project, uploaded).ratio(), project, uploaded) for uploaded in uploaded_videos]
    done = max(seqMatcher, key=lambda x: x[0])
    if done[0] > 0.9:
        result[project] = (result[project][0], "UPLOADED")
    else:
        result[project] = (result[project][0], "        ")

for k, v in result.items():
    print(f"{v[0]} {v[1]}: {k}")