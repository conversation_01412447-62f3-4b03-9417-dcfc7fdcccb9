"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.tsvToJson = void 0;
const promises_1 = __importDefault(require("fs/promises"));
const tsvToJson = async (filePath) => {
    const tsv = await promises_1.default.readFile(filePath, 'utf-8');
    const lines = tsv.split('\n').filter(x => !!x);
    const headers = lines[0].split('\t');
    const data = lines.slice(1).map(line => {
        const values = line.split('\t');
        return headers.reduce((obj, header, i) => {
            obj[header] = values[i];
            return obj;
        }, {});
    });
    return data;
};
exports.tsvToJson = tsvToJson;
//# sourceMappingURL=tsv.js.map