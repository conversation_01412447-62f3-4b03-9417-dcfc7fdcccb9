export interface BaseRequest {
    opCode: string
    machine: string
}

export interface BrowsersInfoEvent extends BaseRequest {
    opCode: 'newBrowser'
    /**
     * List of opening browsers, first browser is active browser
     */
    browsers: BrowserData[]
}

export interface CloseBrowserRequest extends BaseRequest {
    opCode: 'closeBrowser'
}

export interface BrowserData {
    id: string
    type: 'chrome' | 'firefox' | 'edge' | 'brave'
    tabs: string[]
    activeTab: string
}