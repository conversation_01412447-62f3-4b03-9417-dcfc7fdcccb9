import { TemplateProject, templateSegment } from "./templateProject";
import { uuid } from "../libs/utils";

type KeyFrameProperty = "KFTypeScaleX" | "KFTypeScaleY" | "KFTypePositionX" | "KFTypePositionY" | "KFTypeRotation" | "KFTypeOpacity"

/**
 * 
 * @param video Video/text to add keyframe
 * @param offset 0 = start, -N = end max N seconds
 */
export const addKeyFrame = (video: TemplateProject['tracks'][0]['segments'][0], property: KeyFrameProperty, offset: number, value: number) => {
    if (!video.common_keyframes.find(k => k.property_type === property)) {
        const segment = templateSegment('video')
        const keyframe = segment.common_keyframes[0]
        keyframe.id = uuid()
        keyframe.property_type = property
        keyframe.keyframe_list.length = 0
        video.common_keyframes.push(keyframe as never)
    }
    const keyframe = {
        "curveType": "Line",
        "graphID": "",
        "id": uuid(),
        "left_control": {
            "x": 0,
            "y": 0
        },
        "right_control": {
            "x": 0,
            "y": 0
        },
        "string_value": "",
        "time_offset": offset < 0 ? Math.min(video.target_timerange.duration, -offset * 1_000_000) : offset,
        "values": [
            value
        ]
    } 
    video.common_keyframes.find(k => k.property_type === property)!.keyframe_list.push(keyframe)
}