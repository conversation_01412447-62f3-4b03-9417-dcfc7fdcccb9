{
    // See https://go.microsoft.com/fwlink/?LinkId=733558
    // for the documentation about the tasks.json format
    "version": "2.0.0",
    "command": "npm",
    "tasks": [
        {
            "label": "install",
            "type": "shell",
            "command": "npm",
            "args": ["install"]
        },
        {
            "label": "update",
            "type": "shell",
            "command": "npm",
            "args": ["update"]
        },
        {
            "label": "test",
            "type": "shell",
            "command": "npm",
            "args": ["run", "test"]
        },
        {
            "label": "build",
            "type": "shell",
            "group": "build",
            "command": "npm",
            "args": ["run", "watch"]
        }
    ]
}