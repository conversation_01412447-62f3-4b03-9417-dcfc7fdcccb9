"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
require('log-timestamp');
const path_1 = __importDefault(require("path"));
const fs_1 = __importDefault(require("fs"));
const promises_1 = __importDefault(require("fs/promises"));
const project_1 = require("./capcut/project");
const tool_1 = require("./tool");
const templateProject_1 = require("./capcut/templateProject");
const addKeyFrame_1 = require("./capcut/addKeyFrame");
const utils_1 = require("./libs/utils");
const assert_1 = __importDefault(require("assert"));
const trimVideo_1 = require("./libs/trimVideo");
const config_1 = require("./config");
async function main() {
    const [assetDir, templateDir, ...rest] = process.argv.slice(2);
    if (assetDir === '-watch' || assetDir === '-w') {
        if (templateDir === '-f') {
            if (rest[0]) {
                const dirs = (await promises_1.default.readFile(rest[0], 'utf8'))
                    .split('\n')
                    .filter(l => l.trim())
                    .map(l => l.trim());
                await watchToBuildTemplate(dirs);
            }
            else {
                throw new Error("Missing folder description file to watch after '-f'");
            }
        }
        else {
            const dirs = process.argv.slice(3);
            await watchToBuildTemplate(dirs);
        }
        return;
    }
    if (assetDir === '-check') {
        await checkRenderError(templateDir, rest[0]);
        return;
    }
    if (assetDir === '-dump') {
        await copyJson(templateDir || 'C:\\MyData\\CapCut\\projects\\CapCut Drafts\\0428');
        return;
    }
    if (assetDir === '-trim') {
        await (0, trimVideo_1.trimVideoInBatch)(templateDir);
        return;
    }
    if (assetDir === '-split') {
        const [_, ...clips] = process.argv.slice(2);
        for (const clip of clips) {
            await (0, trimVideo_1.splitVideoByScene)(clip);
        }
        return;
    }
    console.log("[Start creating CapCut project]", assetDir, '[Template]', templateDir || 0);
    try {
        await (0, tool_1.findVideoClips)(assetDir);
        await buildTemplate2(assetDir, templateDir, true);
    }
    catch (e) {
        console.error(utils_1.Utils.chalk.paint('Fallback to buildTemplate1', 'red'), utils_1.Utils.chalk.paint(e.message, 'yellow'));
        await buildTemplate1(assetDir, templateDir, true);
    }
    // await test()
}
async function renameVideoToThumbnail() {
    const outPath = config_1.config.OUTPUT_VIDEO_PATH;
    if (!outPath || !fs_1.default.existsSync(outPath)) {
        console.log('Not configured video output path or not exist');
        return;
    }
    const nameFileNoExt = (name) => name.substring(0, name.lastIndexOf('.'));
    while (true) {
        try {
            const files = await promises_1.default.readdir(outPath);
            const videos = [];
            const thumbs = [];
            for (const file of files) {
                const statFile = await promises_1.default.stat(path_1.default.join(outPath, file));
                if (!statFile.isFile() || (Date.now() - statFile.birthtime.getTime()) > 48 * 3600 * 1000) {
                    continue;
                }
                if (file.endsWith('.mp4')) {
                    videos.push(file);
                }
                else if (file.endsWith('.png') || file.endsWith('.jpg')) {
                    thumbs.push(file);
                }
            }
            if (!videos.length || !thumbs.length) {
                await utils_1.Utils.sleep(5_000);
                continue;
            }
            for (const video of videos) {
                const videName = nameFileNoExt(video);
                if (thumbs.some(t => nameFileNoExt(t) === videName)) {
                    continue;
                }
                const matching = thumbs.map(thumb => {
                    const match = new tool_1.CustomSequenceMatcher(null, videName.slice(6), thumb.slice(0, 43)).ratio();
                    return { thumb, match };
                });
                const best = matching.sort((a, b) => b.match - a.match)[0];
                if (best.match > 0.8) {
                    const n = nameFileNoExt(best.thumb) + '.mp4';
                    await promises_1.default.rename(path_1.default.join(outPath, video), path_1.default.join(outPath, n));
                    console.log('Renamed: [', video, '=>>', n, ']');
                }
            }
        }
        catch (e) {
            console.error(e);
        }
        await utils_1.Utils.sleep(5_000);
    }
}
async function test() {
    project_1.CapCutProject.CapCutProjectDir = 'C:/MyData/CapCut/projects/CapCut Drafts';
    const project = new project_1.CapCutProject();
    await project.addClip("C:\\Users\\<USER>\\OneDrive\\YoutubeContent\\The Last Straw Stories\\At Our Anniversary Feast, My MIL Declared My Replacement—She Was Mistaken\\Pictures\\7.jpg", -1, 0, 7, 0);
    await project.addClip("C:\\Users\\<USER>\\OneDrive\\YoutubeContent\\The Last Straw Stories\\At Our Anniversary Feast, My MIL Declared My Replacement—She Was Mistaken\\Pictures\\8.jpg", -1, 0, 6, 0);
    project.addEffect('7399466759058918662', 0, 13, 1);
    project.addSubtitle("lskdjflkwjelk sldkfj lwkejf lksdjf\nlwkej lkisdjflkwjelkf jsdlkfj wlkef j", 4, 3, 2);
    project.addSubtitle("tiếp theo nè", 7, 3, 2);
    await project.addAudio("C:\\Users\\<USER>\\OneDrive\\YoutubeContent\\The Last Straw Stories\\At Our Anniversary Feast, My MIL Declared My Replacement—She Was Mistaken\\new_At Our Anniversary Feast, My MIL Declared My Replacement—She Was Mistaken.mp3", 0, 3);
    project.export("test");
}
function prepare(assetDir, template) {
    const matches = /\'/.exec(assetDir);
    if (matches) {
        throw new Error(`Asset directory contains single quote, please rename it. See below:
${utils_1.Utils.chalk.paint(assetDir, 'red')}
${utils_1.Utils.chalk.paint('^'.padStart(matches.index + 1, ' '), 'magenta')}`);
    }
    (0, templateProject_1.setTemplateFile)(template);
    const customCapCutProjectDir = 'C:/MyData/CapCut/projects/CapCut Drafts';
    if (fs_1.default.existsSync(customCapCutProjectDir)) {
        project_1.CapCutProject.CapCutProjectDir = customCapCutProjectDir;
    }
}
async function buildTemplate1(assetDir, template, showLog = true) {
    showLog && console.log(utils_1.Utils.chalk.paint('buildTemplate1', 'green'));
    prepare(assetDir, template);
    const { imageSubtitleMap, subtitle } = await (0, tool_1.mapImagesToSubtitle)(assetDir, showLog);
    const subtitles = subtitle.compact(45, 2);
    (0, tool_1.magnetImageToSubtitle)(imageSubtitleMap, subtitles);
    const project = new project_1.CapCutProject();
    // find mp3 audio
    const mp3 = (await promises_1.default.readdir(assetDir)).find(f => f.endsWith('.mp3'));
    (0, assert_1.default)(mp3);
    await project.addAudio(path_1.default.join(assetDir, mp3), 0, 0);
    const duration = project.duration + 1;
    imageSubtitleMap[imageSubtitleMap.length - 1][1].start = duration;
    subtitles[subtitles.length - 1].end = duration - 1;
    for (let i = 0; i < imageSubtitleMap.length - 1; i++) {
        const [image, sub] = imageSubtitleMap[i];
        const [_, nextSub] = imageSubtitleMap[i + 1];
        const duration = nextSub.start - sub.start;
        const { segment } = await project.addClip(image, sub.start, 0, duration, 1);
        const scaleFrom = 1 + Math.min(0.25, 0.25 * duration / 20);
        (0, addKeyFrame_1.addKeyFrame)(segment, 'KFTypeScaleX', 0, scaleFrom);
        (0, addKeyFrame_1.addKeyFrame)(segment, 'KFTypeScaleX', -20, 1);
        if (i > 0) {
            await project.addTransition(i - 1, '6724845717472416269', 0.5, 1);
        }
    }
    await addEffectLayer(project, 0, duration, 2);
    for (let i = 0; i < subtitles.length; i++) {
        const sub = subtitles[i];
        project.addSubtitle(sub.text, sub.start, sub.end - sub.start, 3);
    }
    await project.export(getName(assetDir));
}
async function buildTemplate2(assetDir, template, showLog = true) {
    showLog && console.log(utils_1.Utils.chalk.rainbow('buildTemplate2', 'black'));
    prepare(assetDir, template);
    const project = new project_1.CapCutProject();
    const subtitle = await (0, tool_1.readSubtitleFromAudio)(assetDir, showLog);
    const subtitles = subtitle.compact(45, 2);
    // find mp3 audio
    const mp3 = (await promises_1.default.readdir(assetDir)).find(f => f.endsWith('.mp3'));
    (0, assert_1.default)(mp3);
    await project.addAudio(path_1.default.join(assetDir, mp3), 0, 0);
    const duration = project.duration + 1;
    subtitles[subtitles.length - 1].end = project.duration;
    // find opening videos
    const openingVideos = (await promises_1.default.readdir(assetDir))
        .filter(f => f.endsWith('.mp4') || f.startsWith('opening'))
        .map(f => path_1.default.join(assetDir, f))
        .sort((a, b) => b.localeCompare(a));
    const { videos, narratorImg } = await (0, tool_1.findVideoClips)(assetDir);
    let files = utils_1.Utils.shuffleArray(videos.slice());
    files.push(...openingVideos);
    let videoDuration = 0;
    let liveActionEnd = 0;
    while (true) {
        const file = files.pop();
        const probe = await (0, utils_1.getProbe)(file);
        const fileDuration = probe.format.duration;
        if (fileDuration > 90) {
            videos.splice(videos.indexOf(file), 1);
            continue;
        }
        const { segment, video } = await project.addClip(file, videoDuration, 0, fileDuration, 1);
        video.has_audio = false;
        segment.last_nonzero_volume = 0;
        segment.volume = 0;
        videoDuration += Math.max(0.1, fileDuration);
        if (openingVideos.includes(file)) {
            liveActionEnd = videoDuration;
        }
        if (videoDuration >= duration) {
            const cutOff = videoDuration - duration;
            segment.target_timerange.duration -= Math.floor(cutOff * 1_000_000);
            break;
        }
        if (files.length === 0) {
            files = utils_1.Utils.shuffleArray(videos.slice());
        }
    }
    await addEffectLayer(project, liveActionEnd, duration - liveActionEnd, 2);
    await project.addClip(narratorImg, liveActionEnd, 0, duration - liveActionEnd, 3);
    for (let i = 0; i < subtitles.length; i++) {
        const sub = subtitles[i];
        project.addSubtitle(sub.text, sub.start, sub.end - sub.start, 4);
    }
    await project.export(getName(assetDir));
}
async function buildTemplate3(assetDir, template, showLog = true) {
    showLog && console.log(utils_1.Utils.chalk.rainbow('buildTemplate3', 'black'));
    prepare(assetDir, template);
    const project = new project_1.CapCutProject();
    const { imageSubtitleMap, subtitle } = await (0, tool_1.mapImagesToSubtitle)(assetDir, showLog);
    // const subtitle = await readSubtitleFromAudio(assetDir, showLog)
    const subtitles = subtitle.compact(45, 2);
    (0, tool_1.magnetImageToSubtitle)(imageSubtitleMap, subtitles);
    // find mp3 audio
    const mp3 = (await promises_1.default.readdir(assetDir)).find(f => f.endsWith('.mp3'));
    (0, assert_1.default)(mp3);
    await project.addAudio(path_1.default.join(assetDir, mp3), 0, 0);
    const duration = project.duration + 1;
    subtitles[subtitles.length - 1].end = project.duration;
    // find opening videos
    const openingVideos = (await promises_1.default.readdir(assetDir))
        .filter(f => f.endsWith('.mp4') || f.startsWith('opening'))
        .map(f => path_1.default.join(assetDir, f))
        .sort((a, b) => b.localeCompare(a));
    let files = [...openingVideos];
    let videoDuration = 0;
    let liveActionEnd = 0;
    while (files.length) {
        const file = files.pop();
        const probe = await (0, utils_1.getProbe)(file);
        const fileDuration = probe.format.duration;
        const { segment, video } = await project.addClip(file, videoDuration, 0, fileDuration, 1);
        video.has_audio = false;
        segment.last_nonzero_volume = 0;
        segment.volume = 0;
        videoDuration += Math.max(0.1, fileDuration);
        if (openingVideos.includes(file)) {
            liveActionEnd = videoDuration;
        }
        if (videoDuration >= duration) {
            const cutOff = videoDuration - duration;
            segment.target_timerange.duration -= Math.floor(cutOff * 1_000_000);
            break;
        }
    }
    imageSubtitleMap[imageSubtitleMap.length - 1][1].start = duration;
    subtitles[subtitles.length - 1].end = duration - 1;
    for (let i = 0; i < imageSubtitleMap.length - 1; i++) {
        const [image, sub] = imageSubtitleMap[i];
        const [_, nextSub] = imageSubtitleMap[i + 1];
        const duration = nextSub.start - sub.start;
        if (nextSub.start <= liveActionEnd) {
            continue;
        }
        const { segment } = await project.addClip(image, Math.max(sub.start, liveActionEnd), 0, duration - (Math.max(sub.start, liveActionEnd) - sub.start), 1);
        const scaleFrom = 1 + Math.min(0.25, 0.25 * duration / 20);
        (0, addKeyFrame_1.addKeyFrame)(segment, 'KFTypeScaleX', 0, scaleFrom);
        (0, addKeyFrame_1.addKeyFrame)(segment, 'KFTypeScaleX', -20, 1);
        if (i > 0) {
            await project.addTransition(i - 1, '6724845717472416269', 0.5, 1);
        }
    }
    await addEffectLayer(project, liveActionEnd, duration - liveActionEnd, 2);
    for (let i = 0; i < subtitles.length; i++) {
        const sub = subtitles[i];
        project.addSubtitle(sub.text, sub.start, sub.end - sub.start, 3);
    }
    await project.export(getName(assetDir));
}
function getName(str) {
    const [c, ...names] = path_1.default.basename(str).split('-');
    const counter = parseInt(c.trim()) || '';
    if (!counter) {
        names.length = 0;
        names.push(str);
    }
    const parentName = path_1.default.basename(path_1.default.dirname(str))
        .split(/\W/).map(x => x[0]).join('').slice(0, 3);
    return `${parentName}${counter}@${names.join('-').trim()}`;
}
async function watchToBuildTemplate(channelRoot) {
    if (channelRoot.length === 0) {
        throw new Error("Missing channel root directories");
    }
    while (true) {
        for (const root of channelRoot) {
            const channelConfig = await readChannelConfig(root);
            await buildForAllSubDirs(root, channelConfig);
        }
        await utils_1.Utils.sleep(10_000);
    }
}
async function buildForAllSubDirs(rootDir, config) {
    const projects = (await promises_1.default.readdir(rootDir))
        .filter(x => x !== 'done' && fs_1.default.statSync(path_1.default.join(rootDir, x)).isDirectory());
    for (const project of projects) {
        await buildForAllSubDirs(path_1.default.join(rootDir, project), config);
        try {
            const projectDir = path_1.default.join(rootDir, project);
            const clipData = await readClipData(projectDir);
            if (!clipData || !clipData.GeneratedAudio || clipData.GeneratedProject) {
                continue;
            }
            let templateId = 0;
            switch (config.TemplateId) {
                case 1:
                    await buildTemplate1(projectDir, config.UseTemplate, false);
                    break;
                case 2:
                    await buildTemplate2(projectDir, config.UseTemplate, false);
                    break;
                case 3:
                    await buildTemplate3(projectDir, config.UseTemplate, false);
                    break;
                case 4:
                    try {
                        await buildTemplate3(projectDir, config.UseTemplate, false);
                        templateId = 3;
                    }
                    catch (e) {
                        try {
                            await buildTemplate2(projectDir, config.UseTemplate, false);
                            templateId = 2;
                        }
                        catch (e) {
                            await buildTemplate1(projectDir, config.UseTemplate, false);
                            templateId = 1;
                        }
                    }
                    break;
                default:
                    throw new Error(`Unsupported TemplateId ${config.TemplateId} in channel ${rootDir}`);
            }
            clipData.GeneratedProject = true;
            await promises_1.default.writeFile(path_1.default.join(projectDir, 'data.json'), JSON.stringify(clipData));
            console.log("[Created CapCut project]", projectDir, utils_1.Utils.chalk.rainbow(["", "Template1111", "Template2222", "Template333", "Template kết hợp"][config.TemplateId] + (templateId ? `(${templateId})` : ''), 'black'), '[Template]', config.UseTemplate || 'Default');
        }
        catch (e) {
            e.aa = "wer";
            // console.error(e)
        }
    }
}
async function checkRenderError(assetsDir, templateId) {
    switch (templateId) {
        case '1':
            await buildTemplate1(assetsDir);
            break;
        case '2':
            await buildTemplate2(assetsDir);
            break;
        case '3':
            await buildTemplate3(assetsDir);
            break;
        default:
            throw new Error(`Unsupported templateId ${templateId}`);
    }
}
async function readChannelConfig(channelRoot) {
    const channelConfig = JSON.parse(await promises_1.default.readFile(path_1.default.join(channelRoot, 'info.json'), 'utf8'));
    if (channelConfig.UseTemplate) {
        (0, templateProject_1.setTemplateFile)(channelConfig.UseTemplate);
    }
    if (typeof channelConfig.TemplateId !== 'number') {
        throw new Error(`Invalid TemplateId '${channelConfig.TemplateId}' in '${channelRoot}'`);
    }
    return channelConfig;
}
async function readClipData(clipDir) {
    if (!fs_1.default.existsSync(path_1.default.join(clipDir, 'data.json'))) {
        return;
    }
    const clipData = JSON.parse(await promises_1.default.readFile(path_1.default.join(clipDir, 'data.json'), 'utf8'));
    return clipData;
}
async function addEffectLayer(project, start, duration, trackIndex) {
    const effect1 = path_1.default.resolve(path_1.default.join('.', 'materials', 'effect1.mp4'));
    const probe = await (0, utils_1.getProbe)(effect1);
    const effectDuration = probe.format.duration;
    let videoDuration = start;
    let end = start + duration;
    while (true) {
        const { segment, video } = await project.addClip(effect1, videoDuration, 0, effectDuration, trackIndex);
        await project.addEffect0('color_filter', segment, 0.5);
        video.has_audio = false;
        videoDuration += Math.max(0.1, effectDuration);
        if (videoDuration >= end) {
            const cutOff = videoDuration - end;
            segment.target_timerange.duration -= Math.floor(cutOff * 1_000_000);
            break;
        }
    }
}
async function copyJson(dir) {
    let dest = './output';
    // read counter.txt file in dest
    let counter = 0;
    try {
        let content = await promises_1.default.readFile(path_1.default.join(dest, 'counter.txt'), 'utf8');
        counter = parseInt(content);
    }
    catch (e) {
    }
    finally {
        counter++;
        await promises_1.default.writeFile(path_1.default.join(dest, 'counter.txt'), counter.toString());
    }
    console.log('Copy to', counter);
    dest = path_1.default.join(dest, counter.toString());
    await promises_1.default.mkdir(dest, { recursive: true });
    // read all file in path
    let files = await promises_1.default.readdir(dir);
    for (let file of files) {
        let filePath = path_1.default.join(dir, file);
        let stat = await promises_1.default.stat(filePath);
        if (stat.isFile()) {
            let destPath = path_1.default.join(dest, file);
            // read content of file then parse json
            try {
                let content = await promises_1.default.readFile(filePath, 'utf8');
                let json = JSON.parse(content);
                // write json as formatted
                await promises_1.default.writeFile(destPath, JSON.stringify(json, null, 2));
            }
            catch (e) {
            }
        }
    }
}
main();
renameVideoToThumbnail();
//# sourceMappingURL=index.js.map