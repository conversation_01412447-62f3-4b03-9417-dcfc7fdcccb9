{"version": 3, "file": "addEffect.js", "sourceRoot": "", "sources": ["../../src/capcut/addEffect.ts"], "names": [], "mappings": ";;;;;;AAAA,gDAAuB;AACvB,4CAAmB;AACnB,2DAA6B;AAC7B,4CAAmB;AACnB,yCAAqC;AAErC,uDAAqF;AACrF,yCAAmD;AACnD,uCAA+C;AAExC,MAAM,SAAS,GAAG,KAAK,EAAE,OAAwB,EAAE,gBAA0B,EAAE,KAAa,EAAE,QAAgB,EAAE,aAAqB,CAAC,EAA6D,EAAE;IACxM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,SAAS,CAAC,CAAA;IACrC,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,SAAS,CAAC,CAAA;IAC3C,MAAM,UAAU,GAAG,MAAM,IAAA,qBAAa,EAAC,gBAAgB,CAAC,CAAA;IAExD,MAAM,WAAW,GAAG,IAAA,iCAAe,GAAE,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC,CAAC,CAAA;IAChE,WAAW,CAAC,EAAE,GAAG,IAAA,YAAI,GAAE,CAAA;IACvB,WAAW,CAAC,WAAW,GAAG,gBAAgB,CAAA;IAC1C,WAAW,CAAC,SAAS,GAAG,gBAAgB,CAAA;IACxC,WAAW,CAAC,IAAI,GAAG,UAAU,CAAA;IAC7B,WAAW,CAAC,IAAI,GAAG,SAAS,GAAG,CAAC,OAAO,CAAC,SAAS,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC,CAAA;IAC3E,WAAW,CAAC,aAAa,GAAG,EAAE,CAAA;IAC9B,MAAM,KAAK,GAAG,cAAI,CAAC,IAAI,CAAC,UAAU,EAAE,YAAY,CAAC,CAAA;IACjD,IAAI,YAAE,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC;QACvB,MAAM,OAAO,GAAG,YAAE,CAAC,YAAY,CAAC,KAAK,EAAE,MAAM,CAAC,CAAA;QAC9C,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAA;QAChC,WAAW,CAAC,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC,EAAE,aAAa,EAAE,CAAC,CAAC,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC,UAAU,EAAE,KAAK,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,CAAA;IACvJ,CAAC;IACD,IAAA,mBAAS,EAAC,WAAW,EAAE,gBAAgB,CAAC,CAAA;IACxC,OAAO,CAAC,SAAS,CAAC,aAAa,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;IAEjD,MAAM,KAAK,GAAG,IAAA,mBAAQ,EAAC,OAAO,EAAE,QAAQ,EAAE,UAAU,CAAC,CAAA;IACrD,MAAM,OAAO,GAAG,IAAA,iCAAe,EAAC,QAAQ,CAAC,CAAA;IACzC,OAAO,CAAC,EAAE,GAAG,IAAA,YAAI,GAAE,CAAA;IACnB,OAAO,CAAC,WAAW,GAAG,WAAW,CAAC,EAAE,CAAA;IACpC,OAAO,CAAC,gBAAgB,GAAG;QACvB,KAAK,EAAE,KAAK;QACZ,QAAQ,EAAE,QAAQ;KACrB,CAAA;IACD,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAc,CAAC,CAAA;IAEnC,OAAO,CAAC,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,QAAQ,EAAE,KAAK,GAAG,QAAQ,CAAC,CAAA;IAE/D,OAAO,WAAW,CAAA;AACtB,CAAC,CAAA;AAlCY,QAAA,SAAS,aAkCrB;AAGM,MAAM,UAAU,GAAG,KAAK,EAAE,OAAwB,EAAE,MAAoB,EAAE,OAAoD,EAAE,KAAa,EAAiB,EAAE;IACnK,MAAM,UAAU,GAAG,cAAI,CAAC,IAAI,CAAC,YAAE,CAAC,OAAO,EAAE,EAAE,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAA;IAChF,MAAM,IAAI,GAAG,YAAE,CAAC,WAAW,CAAC,UAAU,CAAC;SAClC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,YAAE,CAAC,QAAQ,CAAC,cAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;SAC7G,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,EAAC,CAAC,CAAC;SAC5C,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;IAC7F,MAAM,GAAG,GAAG,cAAI,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,WAAW,EAAE,SAAS,CAAC,CAAA;IAClF,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,kBAAG,CAAC,QAAQ,CAAC,cAAI,CAAC,IAAI,CAAC,GAAG,EAAE,cAAc,CAAC,EAAE,MAAM,CAAC,CAAC,CAAA;IACtF,MAAM,MAAM,GAAG,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,CAAA;IACzE,IAAI,CAAC,MAAM,EAAE,CAAC;QACV,MAAM,IAAI,KAAK,CAAC,UAAU,MAAM,YAAY,CAAC,CAAA;IACjD,CAAC;IACD,MAAM,UAAU,GAAG;QACf,eAAe,EAAE,EAAE;QACnB,yBAAyB,EAAE,EAAE;QAC7B,mBAAmB,EAAE,CAAC;QACtB,4BAA4B,EAAE,EAAE;QAChC,cAAc,EAAE,IAAI;QACpB,aAAa,EAAE,EAAE;QACjB,eAAe,EAAE,EAAE;QACnB,kBAAkB,EAAE;YAChB,qBAAqB,EAAE,EAAE;YACzB,qBAAqB,EAAE,EAAE;YACzB,mBAAmB,EAAE,EAAE;SAC1B;QACD,0BAA0B,EAAE,CAAC;QAC7B,WAAW,EAAE,MAAM,CAAC,QAAQ;QAC5B,6BAA6B,EAAE,KAAK;QACpC,iBAAiB,EAAE,EAAE;QACrB,oBAAoB,EAAE,EAAE;QACxB,YAAY,EAAE,EAAE;QAChB,IAAI,EAAE,IAAA,YAAI,GAAE;QACZ,eAAe,EAAE,EAAE;QACnB,kBAAkB,EAAE,CAAC;QACrB,eAAe,EAAE,EAAE;QACnB,wBAAwB,EAAE,EAAE;QAC5B,MAAM,EAAE,QAAQ;QAChB,UAAU,EAAE,EAAE;QACd,MAAM,EAAE,cAAI,CAAC,IAAI,CAAC,GAAG,EAAE,MAAM,CAAC,IAAI,CAAC;QACnC,UAAU,EAAE,KAAK;QACjB,YAAY,EAAE,EAAE;QAChB,aAAa,EAAE,MAAM,CAAC,UAAU;QAChC,iBAAiB,EAAE,CAAC;QACpB,UAAU,EAAE,MAAM;QAClB,mBAAmB,EAAE,EAAE;QACvB,YAAY,EAAE,IAAI;QAClB,MAAM,EAAE,UAAU;QAClB,OAAO,EAAE,CAAC;QACV,SAAS,EAAE,EAAE;KAChB,CAAA;IACD,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,UAAmB,CAAC,CAAA;IACnD,OAAO,CAAC,IAAK,CAAC,KAAK,GAAG,KAAK,CAAA;IAC3B,OAAO,CAAC,mBAAmB,CAAC,IAAI,CAAC,UAAU,CAAC,EAAW,CAAC,CAAA;AAC5D,CAAC,CAAA;AArDY,QAAA,UAAU,cAqDtB"}