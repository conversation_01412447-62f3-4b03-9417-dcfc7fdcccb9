"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.addEffect0 = exports.addEffect = void 0;
const path_1 = __importDefault(require("path"));
const fs_1 = __importDefault(require("fs"));
const promises_1 = __importDefault(require("fs/promises"));
const os_1 = __importDefault(require("os"));
const addTrack_1 = require("./addTrack");
const templateProject_1 = require("./templateProject");
const utils_1 = require("../libs/utils");
const effects_1 = require("./effects");
const addEffect = async (project, effectResourceId, start, duration, trackIndex = 0) => {
    start = Math.floor(start * 1_000_000);
    duration = Math.floor(duration * 1_000_000);
    const effectPath = await (0, utils_1.getEffectPath)(effectResourceId);
    const videoEffect = (0, templateProject_1.templateProject)().materials.video_effects[0];
    videoEffect.id = (0, utils_1.uuid)();
    videoEffect.resource_id = effectResourceId;
    videoEffect.effect_id = effectResourceId;
    videoEffect.path = effectPath;
    videoEffect.name = "effect " + (project.materials.video_effects.length + 1);
    videoEffect.adjust_params = [];
    const extra = path_1.default.join(effectPath, "extra.json");
    if (fs_1.default.existsSync(extra)) {
        const content = fs_1.default.readFileSync(extra, "utf8");
        const json = JSON.parse(content);
        videoEffect.adjust_params = json.setting.effect_adjust_params.map((p) => ({ default_value: p.default, name: p.effect_key, value: p.default }));
    }
    (0, effects_1.setEffect)(videoEffect, effectResourceId);
    project.materials.video_effects.push(videoEffect);
    const track = (0, addTrack_1.addTrack)(project, "effect", trackIndex);
    const segment = (0, templateProject_1.templateSegment)("effect");
    segment.id = (0, utils_1.uuid)();
    segment.material_id = videoEffect.id;
    segment.target_timerange = {
        start: start,
        duration: duration
    };
    track.segments.push(segment);
    project.duration = Math.max(project.duration, start + duration);
    return videoEffect;
};
exports.addEffect = addEffect;
const addEffect0 = async (project, nameId, segment, alpha) => {
    const defaultDir = path_1.default.join(os_1.default.homedir(), "AppData", "Local", "CapCut", "Apps");
    const dirs = fs_1.default.readdirSync(defaultDir)
        .filter(d => fs_1.default.statSync(path_1.default.join(defaultDir, d)).isDirectory() && d.split('.').every(x => /^\d+$/g.test(x)))
        .map(d => ({ d, v: d.split('.').map(Number) }))
        .sort((a, b) => a.v[0] - b.v[0] || a.v[1] - b.v[1] || a.v[2] - b.v[2] || a.v[3] - b.v[3]);
    const dir = path_1.default.join(defaultDir, dirs[dirs.length - 1].d, "Resources", "MixMode");
    const mixMode = JSON.parse(await promises_1.default.readFile(path_1.default.join(dir, 'MixMode.json'), 'utf8'));
    const effect = mixMode.resourceList.find((e) => e.nameId === nameId);
    if (!effect) {
        throw new Error(`Effect ${nameId} not found`);
    }
    const effectData = {
        "adjust_params": [],
        "algorithm_artifact_path": "",
        "apply_target_type": 0,
        "beauty_face_auto_preset_id": "",
        "bloom_params": null,
        "category_id": "",
        "category_name": "",
        "color_match_info": {
            "source_feature_path": "",
            "target_feature_path": "",
            "target_image_path": ""
        },
        "covering_relation_change": 0,
        "effect_id": effect.effectId,
        "enable_skin_tone_correction": false,
        "exclusion_group": [],
        "face_adjust_params": [],
        "formula_id": "",
        "id": (0, utils_1.uuid)(),
        "intensity_key": "",
        "item_effect_type": 0,
        "lumi_hub_path": "",
        "multi_language_current": "",
        "name": "Screen",
        "panel_id": "",
        "path": path_1.default.join(dir, effect.path),
        "platform": "all",
        "request_id": "",
        "resource_id": effect.resourceId,
        "source_platform": 0,
        "sub_type": "none",
        "third_resource_id": "",
        "time_range": null,
        "type": "mix_mode",
        "value": 1,
        "version": ""
    };
    project.materials.effects.push(effectData);
    segment.clip.alpha = alpha;
    segment.extra_material_refs.push(effectData.id);
};
exports.addEffect0 = addEffect0;
//# sourceMappingURL=addEffect.js.map