
async function unsubscribeAllChannels() {
    const sleep = (ms) => new Promise(resolve => setTimeout(resolve, ms || (1000 + Math.random() * 1000)))
    const channels = document.getElementsByTagName("ytd-subscription-notification-toggle-button-renderer-next")
    // debugger;
    for (let i = 0; i < channels.length; i++) {
        var button = channels[i]
        button.scroll({ behavior: 'instant', top: 200 })
        await sleep()
        var subButton = button.getElementsByClassName("yt-spec-touch-feedback-shape__fill")[0]
        subButton.click()
        await sleep()
        var xpath = "//yt-formatted-string[contains(text(),'Unsubscribe')]"
        var unsubscribeButton = document.evaluate(xpath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue
        unsubscribeButton.click()
        await sleep()
        var confirmButton = document.getElementById("confirm-button")
        var button = confirmButton.getElementsByTagName("button")[0]
        button.click()
        await sleep()
    }
}