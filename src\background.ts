// background.js

let ws: WebSocket
const SERVER_URL = 'ws://localhost:3000' // Replace with your server URL

function connectWebSocket() {
  if (ws && ws.readyState === WebSocket.OPEN) {
    console.log("WebSocket already open.")
    return
  }

  ws = new WebSocket(SERVER_URL)

  ws.onopen = () => {
    console.log("WebSocket connected to server.")
    // Optionally send a message to the server to identify this client
    ws.send(JSON.stringify({ type: "client_connected", clientId: "chrome_extension" }))
  }

  ws.onmessage = (event) => {
    console.log("Message from server:", event.data)
    try {
      const command = JSON.parse(event.data)
      handleServerCommand(command)
    } catch (e) {
      console.error("Failed to parse server command:", e)
    }
  }

  ws.onclose = () => {
    console.log("WebSocket disconnected. Attempting to reconnect in 5 seconds...")
    setTimeout(connectWebSocket, 5000) // Reconnect on close
  }

  ws.onerror = (error) => {
    console.error("WebSocket error:", error)
    ws.close() // Close to trigger reconnect
  }
}

// Initial connection attempt
connectWebSocket()

// Function to handle commands received from the server
async function handleServerCommand(command: any) {
  const currentTab = await getCurrentActiveTab()
  if (!currentTab || !currentTab.id) {
    console.warn("No active tab found to execute command.")
    return
  }

  try {
    switch (command.action) {
      case "navigateTo":
        if (command.url) {
          await chrome.tabs.update(currentTab.id, { url: command.url })
          console.log(`Mapsd to: ${command.url}`)
        }
        break
      case "findElementBySelector":
      case "clickElement":
      case "typeText":
      case "getElementPosition":
        // Forward these commands to the content script of the active tab
        const response = await chrome.tabs.sendMessage(currentTab.id, command)
        console.log("Content script response:", response)
        // Optionally send the response back to the server
        ws.send(JSON.stringify({ type: "content_script_response", command: command.action, data: response }))
        break
      // Add more cases for other actions your server might send
      default:
        console.warn("Unknown command received:", command)
    }
  } catch (error: any) {
    console.error("Error handling server command:", error)
    // Send error back to server if possible
    ws.send(JSON.stringify({ type: "error", command: command.action, message: error.message }))
  }
}

// Helper to get the currently active tab
async function getCurrentActiveTab() {
  const tabs = await chrome.tabs.query({ active: true, currentWindow: true })
  return tabs[0]
}

// Listen for messages from content scripts (e.g., responses from element interactions)
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  if (message.type === "content_script_result" && ws && ws.readyState === WebSocket.OPEN) {
    console.log("Received result from content script:", message.data)
    ws.send(JSON.stringify({ type: "content_script_result", data: message.data }))
  }
  // If you need to send a response back to the content script immediately
  // sendResponse({ status: "received" })
})