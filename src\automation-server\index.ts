import WebSocket from 'ws'
import { BaseRequest, BrowserData, BrowsersInfoEvent } from './types'
const machines = new Map<string, Machine>()
export function startServer() {
    const wss = new WebSocket.Server({ port: 3456 })
    wss.on('connection', (ws) => {
        let machine: string | undefined = undefined
        ws.on('message', (message) => {
            try {
                const data = JSON.parse(message.toString('utf-8')) as BaseRequest
                if (data.opCode === 'login') {
                    machine = data.machine
                    if (machine) {
                        machines.set(machine, new Machine(machine, ws))
                    } else {
                        console.error('No machine data to login', data)
                    }
                } else if (machine) {
                    machines.get(machine)?.onMessage(data)
                }
            } catch (e) {
                console.error('Handle message error', e, message.toString('utf-8'), ws.url)
            }
        })
        ws.on('close', () => {
            if (machine) {
                machines.delete(machine)
            }
            machine = undefined
        })
    })
}

class Machine {
    public browsers: BrowserData[] = []
    
    constructor(public id: string, public ws: WebSocket) {
    }

    onMessage(data: any) {
        const opCode = data.opCode
        if ((this as any)[opCode]) {
            try {
                (this as any)[opCode](data)
            } catch (e) {
                console.error('Handle message', opCode, e)
            }
        } else {
            console.log('Unknown opCode', opCode)
        }
    }

    updateBrowsersInfo(data: BrowsersInfoEvent) {
        this.browsers = data.browsers
    }

    get activeBrowser() {
        return this.browsers[0]
    }
}

